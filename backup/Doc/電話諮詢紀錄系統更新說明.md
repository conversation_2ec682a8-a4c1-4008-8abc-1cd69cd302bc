# 電話諮詢紀錄系統 - 最新更新功能

## 🆕 新增功能

### 1. 與案主關係下拉選單
- ✅ **位置**: 詳細表單 > 申請人資訊 > 與案主關係
- ✅ **功能**: 
  - 提供預設選項：本人、父親、母親、公公、婆婆、雙親(公婆/父母)、丈夫、妻子、爺爺(外公)、奶奶(外婆)、朋友、其它
  - 選擇「其它」時自動顯示文字輸入框
  - 選擇其他選項時隱藏輸入框

### 2. 自動載入接聽人員資訊
- ✅ **資料來源**: 從 cookie `vghks_account` 取得接聽人員代碼
- ✅ **自動查詢**: 透過 API 查詢 users 表格取得對應姓名
- ✅ **適用範圍**: 簡易表單和詳細表單都支援
- ✅ **欄位**:
  - 接聽人員代碼（自動填入，唯讀）
  - 接聽人員姓名（自動填入）

## 🔧 技術實作

### 前端功能
```javascript
// 自動載入接聽人員資訊
function loadContactPersonInfo() {
    const contactPersonCode = getCookie('vghks_account');
    if (contactPersonCode) {
        $('#contact_person_code').val(contactPersonCode);
        // AJAX 查詢姓名...
    }
}

// 關係選擇處理
$('#apply_relationship_select').change(function() {
    const selectedValue = $(this).val();
    if (selectedValue === '其它') {
        $('#apply_relationship_other_container').show();
    } else {
        $('#apply_relationship_other_container').hide();
    }
});
```

### 後端 API
```javascript
// 新增 API: 取得接聽人員資訊
router.post('/get-contact-person-info', apiVerifyMiddleware, async function (req, res, next) {
    const { contact_person_code } = req.body;
    const userInfo = await PhoneConsultationRepository.getUserInfoByAccount(contact_person_code);
    // 回傳姓名和帳號...
});
```

### 資料庫查詢
```javascript
// Repository 新增方法
exports.getUserInfoByAccount = (account) => {
    // 查詢 users 表格取得 name 和 account
};
```

## 📋 表單結構更新

### 詳細表單 (detailed_record.ejs)
```html
<!-- 與案主關係下拉選單 -->
<select class="form-select" id="apply_relationship_select">
    <option value="">(請選擇)</option>
    <option value="本人">本人</option>
    <option value="父親">父親</option>
    <!-- ... 其他選項 ... -->
    <option value="其它">其它</option>
</select>

<!-- 其他關係輸入框（條件顯示） -->
<div id="apply_relationship_other_container" style="display: none;">
    <input type="text" id="apply_relationship_other" placeholder="請輸入關係">
</div>

<!-- 接聽人員資訊 -->
<input type="text" id="contact_person_code" readonly>
<input type="text" id="contact_person_name" required>
```

### 簡易表單 (record.ejs)
```html
<!-- 接聽人員資訊 -->
<input type="text" id="contact_person_code" readonly>
<input type="text" id="contact_person_name" required>
```

## 🎯 用戶體驗改善

### 1. 自動化程度提升
- ✅ 接聽人員資訊自動填入，減少手動輸入
- ✅ 關係選擇智能化，提供常用選項

### 2. 資料一致性
- ✅ 接聽人員代碼直接從登入 cookie 取得，確保準確性
- ✅ 姓名從資料庫查詢，避免輸入錯誤

### 3. 介面友善性
- ✅ 條件式顯示輸入框，介面更簡潔
- ✅ 預設選項涵蓋常見關係，提升效率

## 🔄 資料處理流程

### 頁面載入時
1. 從 cookie 取得 `vghks_account`
2. 自動填入接聽人員代碼欄位
3. 發送 API 請求查詢對應姓名
4. 自動填入接聽人員姓名欄位

### 關係選擇時
1. 監聽下拉選單變更事件
2. 判斷是否選擇「其它」
3. 動態顯示/隱藏文字輸入框
4. 設定必填驗證規則

### 表單提交時
1. 收集關係資料（下拉選單值或自訂輸入）
2. 包含接聽人員代碼和姓名
3. 驗證必填欄位
4. 提交到後端處理

## 🚀 使用方式

### 1. 訪問詳細表單
- URL: `/phoneConsultationRecords/create-detailed`
- 接聽人員資訊會自動載入
- 在「與案主關係」選擇適當選項

### 2. 訪問簡易表單
- URL: `/phoneConsultationRecords/create`
- 接聽人員資訊會自動載入

### 3. 編輯現有記錄
- 編輯時會保留原有的關係資料
- 接聽人員資訊仍會自動載入當前用戶

## ✅ 測試檢查項目

- [ ] 頁面載入時接聽人員代碼自動填入
- [ ] 接聽人員姓名自動查詢並填入
- [ ] 關係下拉選單正常運作
- [ ] 選擇「其它」時輸入框正常顯示
- [ ] 選擇其他選項時輸入框正常隱藏
- [ ] 表單提交時資料正確收集
- [ ] 編輯模式下資料正確載入

## 🔒 安全考量

- ✅ API 請求需要身份驗證
- ✅ 只查詢必要的用戶資訊（姓名、帳號）
- ✅ 接聽人員代碼從安全的 cookie 取得
- ✅ 輸入驗證和清理

這些更新大幅提升了系統的易用性和資料準確性，讓用戶能更快速、準確地建立電話諮詢記錄。
