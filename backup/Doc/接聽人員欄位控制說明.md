# 接聽人員欄位控制功能說明

## 🎯 功能需求

當接聽人員代碼欄位有輸入時，接聽人員姓名欄位應該變成 disabled（禁用），因為姓名是自動從資料庫查詢取得的，不應該手動修改。

## ✅ 實現功能

### 1. 預設狀態
- ✅ 接聽人員代碼：自動從 cookie `vghks_account` 填入，readonly
- ✅ 接聽人員姓名：當有代碼時自動設為 readonly

### 2. 動態控制
- ✅ 當代碼欄位有值時 → 姓名欄位變為 readonly
- ✅ 當代碼欄位為空時 → 姓名欄位可以手動輸入
- ✅ 代碼變更時自動查詢對應姓名

### 3. 視覺回饋
- ✅ readonly 欄位有特殊樣式（灰色背景）
- ✅ 滑鼠游標顯示為 not-allowed

## 🔧 技術實現

### HTML 結構
```html
<!-- 接聽人員代碼（自動填入，readonly） -->
<input type="text" class="form-control" id="contact_person_code" readonly>

<!-- 接聽人員姓名（動態 readonly） -->
<input type="text" class="form-control required-field" id="contact_person_name" required readonly>
```

### CSS 樣式
```css
.form-control[readonly] {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    cursor: not-allowed;
}
```

### JavaScript 邏輯

#### 1. 頁面載入時自動處理
```javascript
function loadContactPersonInfo() {
    const contactPersonCode = getCookie('vghks_account');
    if (contactPersonCode) {
        $('#contact_person_code').val(contactPersonCode);
        $('#contact_person_name').prop('readonly', true);
        
        // AJAX 查詢姓名...
    } else {
        $('#contact_person_name').prop('readonly', false);
    }
}
```

#### 2. 代碼欄位變更監聽
```javascript
$('#contact_person_code').on('input', function() {
    const codeValue = $(this).val().trim();
    const nameField = $('#contact_person_name');
    
    if (codeValue) {
        // 有代碼 → readonly + 自動查詢
        nameField.prop('readonly', true);
        // AJAX 查詢姓名...
    } else {
        // 無代碼 → 允許手動輸入
        nameField.prop('readonly', false).val('');
    }
});
```

## 🎨 用戶體驗

### 正常流程
1. **頁面載入** → 代碼自動填入 → 姓名自動查詢並填入 → 姓名欄位變為 readonly
2. **視覺提示** → readonly 欄位有灰色背景，清楚表示不可編輯
3. **防止誤操作** → 用戶無法意外修改自動查詢的姓名

### 異常處理
1. **查詢失敗** → 姓名欄位變為可編輯，允許手動輸入
2. **無代碼** → 姓名欄位可編輯，支援手動輸入
3. **代碼清空** → 姓名欄位變為可編輯，內容清空

## 📋 適用範圍

### 詳細表單 (`detailed_record.ejs`)
- ✅ 接聽人員代碼欄位控制
- ✅ 接聽人員姓名欄位動態 readonly

### 簡易表單 (`record.ejs`)
- ✅ 接聽人員代碼欄位控制
- ✅ 接聽人員姓名欄位動態 readonly

## 🔄 狀態轉換

```
頁面載入
    ↓
取得 cookie 代碼
    ↓
有代碼？
├─ 是 → 設定 readonly → AJAX 查詢姓名 → 填入姓名
└─ 否 → 允許手動輸入

代碼欄位變更
    ↓
有輸入值？
├─ 是 → 設定 readonly → AJAX 查詢姓名 → 填入姓名
└─ 否 → 移除 readonly → 清空姓名
```

## 🛡️ 資料完整性

### 確保資料準確性
- ✅ 自動查詢避免手動輸入錯誤
- ✅ readonly 防止意外修改
- ✅ 代碼與姓名的一致性

### 容錯機制
- ✅ 查詢失敗時允許手動輸入
- ✅ 無代碼時支援完全手動操作
- ✅ 錯誤提示和日誌記錄

## 🎯 優點總結

1. **自動化** - 減少手動輸入，提升效率
2. **準確性** - 避免姓名輸入錯誤
3. **一致性** - 確保代碼與姓名對應正確
4. **用戶友善** - 清楚的視覺提示和狀態控制
5. **容錯性** - 完善的異常處理機制

這個功能確保了接聽人員資訊的準確性和一致性，同時提供良好的用戶體驗。
