# 接聽人員和主責客專功能完整說明

## 🎯 功能需求

### 接聽人員
- **代碼來源**: 從 cookie `vghks_account` 自動取得
- **姓名查詢**: 從 users 資料表自動查詢對應姓名
- **欄位狀態**: 代碼和姓名都設為 readonly

### 主責客專
- **代碼輸入**: 手動輸入代碼
- **姓名查詢**: 輸入代碼後從 users 資料表自動查詢姓名
- **欄位狀態**: 代碼可編輯，姓名根據是否有代碼動態設為 readonly

## ✅ 完整實作

### 1. HTML 結構
```html
<!-- 接聽人員代碼（從 cookie 自動填入，readonly） -->
<input type="text" class="form-control" id="contact_person_code" readonly>

<!-- 接聽人員姓名（自動查詢填入，readonly） -->
<input type="text" class="form-control required-field" id="contact_person_name" required readonly>

<!-- 主責客專代碼（可手動輸入） -->
<input type="text" class="form-control" id="duty_member_code">

<!-- 主責客專姓名（根據代碼自動查詢，動態 readonly） -->
<input type="text" class="form-control" id="duty_member_name" readonly>
```

### 2. CSS 樣式
```css
.form-control[readonly] {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    cursor: not-allowed;
}
```

### 3. JavaScript 功能

#### 頁面載入時自動處理
```javascript
$(document).ready(function() {
    // 初始化
    initializePage();
    // 綁定事件
    bindEvents();
    // 載入接聽人員資訊
    loadContactPersonInfo();
    // 編輯模式載入資料
    if (isUpdateMode && recordId) {
        loadDetailedRecordData();
    }
});
```

#### 接聽人員資訊自動載入
```javascript
function loadContactPersonInfo() {
    // 從 cookie 取得接聽人員代碼
    const contactPersonCode = getCookie('vghks_account');
    if (contactPersonCode) {
        $('#contact_person_code').val(contactPersonCode);
        $('#contact_person_name').prop('readonly', true);
        
        // AJAX 查詢姓名
        $.ajax({
            type: 'POST',
            url: '/phoneConsultationRecords/get-contact-person-info',
            contentType: 'application/json',
            data: JSON.stringify({ contact_person_code: contactPersonCode }),
            success: function (data) {
                if (data.state === "success" && data.data) {
                    $('#contact_person_name').val(data.data.name);
                }
            },
            error: function () {
                console.log('無法取得接聽人員資訊');
                $('#contact_person_name').prop('readonly', false);
            }
        });
    } else {
        $('#contact_person_name').prop('readonly', false);
    }

    // 初始化主責客專狀態
    const dutyMemberCode = $('#duty_member_code').val().trim();
    if (dutyMemberCode) {
        $('#duty_member_name').prop('readonly', true);
    } else {
        $('#duty_member_name').prop('readonly', false);
    }
}
```

#### 動態事件監聽
```javascript
// 接聽人員代碼變更事件（雖然是 readonly，但保留以防需要）
$('#contact_person_code').on('input', function() {
    const codeValue = $(this).val().trim();
    const nameField = $('#contact_person_name');
    
    if (codeValue) {
        nameField.prop('readonly', true);
        // AJAX 查詢姓名...
    } else {
        nameField.prop('readonly', false).val('');
    }
});

// 主責客專代碼變更事件
$('#duty_member_code').on('input', function() {
    const codeValue = $(this).val().trim();
    const nameField = $('#duty_member_name');
    
    if (codeValue) {
        nameField.prop('readonly', true);
        // AJAX 查詢姓名...
    } else {
        nameField.prop('readonly', false).val('');
    }
});
```

#### Cookie 讀取函數
```javascript
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return '';
}
```

## 🔄 工作流程

### 接聽人員流程
1. **頁面載入** → 從 cookie `vghks_account` 取得代碼
2. **自動填入** → 代碼填入接聽人員代碼欄位（readonly）
3. **自動查詢** → AJAX 查詢 users 資料表取得姓名
4. **自動填入** → 姓名填入接聽人員姓名欄位（readonly）

### 主責客專流程
1. **手動輸入** → 用戶在主責客專代碼欄位輸入代碼
2. **觸發事件** → input 事件監聽器被觸發
3. **設定狀態** → 姓名欄位變為 readonly
4. **自動查詢** → AJAX 查詢 users 資料表取得姓名
5. **自動填入** → 姓名填入主責客專姓名欄位

## 🛡️ 錯誤處理

### AJAX 查詢失敗
- **接聽人員**: 如果查詢失敗，姓名欄位變為可編輯
- **主責客專**: 如果查詢失敗，清空姓名欄位但保持 readonly

### 無 Cookie 情況
- **接聽人員**: 如果沒有 cookie，代碼和姓名欄位都可編輯

### 無代碼情況
- **主責客專**: 如果代碼為空，姓名欄位變為可編輯並清空內容

## 🎨 視覺效果

### readonly 欄位樣式
- **背景色**: 淺灰色 (#f8f9fa)
- **邊框色**: 灰色 (#dee2e6)
- **文字色**: 深灰色 (#6c757d)
- **游標**: not-allowed

### 狀態指示
- **自動填入**: readonly 欄位清楚表示為自動填入
- **可編輯**: 正常樣式表示可手動輸入
- **必填**: 紅色左邊框表示必填欄位

## 🔧 後端 API

### 查詢人員資訊 API
```javascript
// 路由: POST /phoneConsultationRecords/get-contact-person-info
// 參數: { contact_person_code: "代碼" }
// 回傳: { state: "success", data: { name: "姓名", account: "帳號" } }
```

### Repository 查詢方法
```javascript
// 方法: getUserInfoByAccount(account)
// 查詢: SELECT name, account FROM users WHERE account = ?
// 回傳: { name: "姓名", account: "帳號" } 或 null
```

## 🚀 測試檢查項目

### 接聽人員功能
- [ ] 頁面載入時代碼自動從 cookie 填入
- [ ] 代碼欄位為 readonly（灰色背景）
- [ ] 姓名自動查詢並填入
- [ ] 姓名欄位為 readonly（灰色背景）
- [ ] 查詢失敗時姓名欄位變為可編輯

### 主責客專功能
- [ ] 代碼欄位可正常輸入
- [ ] 輸入代碼後姓名欄位變為 readonly
- [ ] 姓名自動查詢並填入
- [ ] 清空代碼後姓名欄位變為可編輯
- [ ] 查詢失敗時姓名欄位清空但保持 readonly

### 視覺效果
- [ ] readonly 欄位有灰色背景
- [ ] readonly 欄位游標為 not-allowed
- [ ] 可編輯欄位樣式正常

### 資料提交
- [ ] 表單提交時包含正確的代碼和姓名
- [ ] 必填驗證正常運作

## 🎯 優點總結

1. **自動化**: 接聽人員資訊完全自動化，減少手動輸入
2. **準確性**: 從資料庫查詢確保姓名正確性
3. **一致性**: 代碼與姓名的對應關係一致
4. **用戶友善**: 清楚的視覺提示和狀態控制
5. **容錯性**: 完善的錯誤處理機制

這個實作確保了接聽人員和主責客專資訊的準確性和易用性，提供了完整的自動化功能。
