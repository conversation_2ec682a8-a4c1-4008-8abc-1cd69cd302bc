# 電話諮詢紀錄系統 - 完整功能說明

## 📋 系統概述

基於您提供的 A4.txt 表單內容，我們創建了一個完整的電話諮詢紀錄系統，包含簡易版和詳細版兩種表單模式，完全模仿 homeVisitRecords 的風格和架構。

## 🗂️ 檔案結構

### 1. 資料庫 Schema
- **檔案**: `sql_schema_phone_consultation_records.sql`
- **表格**: `cs_phone_consultation_records`
- **特色**: 支援 JSON 格式儲存多選項目，完整的欄位設計

### 2. 後端檔案
- **Repository**: `admin/repository/phoneConsultationRecords.js`
- **Routes**: `admin/routes/phoneConsultationRecords.js`
- **路由配置**: 已更新 `admin/router.js`

### 3. 前端頁面
- **列表頁**: `admin/views/phoneConsultationRecords/index.ejs`
- **簡易表單**: `admin/views/phoneConsultationRecords/record.ejs`
- **詳細表單**: `admin/views/phoneConsultationRecords/detailed_record.ejs`

## 🎯 功能特色

### 📊 主列表頁面
- ✅ 完整搜尋功能（日期範圍、縣市區域、接觸人員、狀況、關鍵字）
- ✅ 響應式卡片式顯示
- ✅ 分頁功能（每頁50筆）
- ✅ Ajax 動態載入
- ✅ 兩種新增模式：簡易版 & 詳細版

### 📝 簡易表單（原版）
- ✅ 基本電話諮詢記錄功能
- ✅ 必要欄位驗證
- ✅ 縣市區域聯動選擇

### 📋 詳細表單（基於 A4.txt）
根據您提供的 A4.txt 內容，包含以下完整功能：

#### 基本資訊
- 諮詢日期、接聽人員代碼、接聽人員姓名

#### 申請人資訊
- 申請人姓名、性別、電話、與案主關係
- Email、主責客專代碼、主責客專姓名

#### 資料來源（多選）
- 1966長照專線、衛生局、社會局、醫院、診所
- 居家護理所、照管中心、親友介紹、網路
- 報章雜誌、電視廣播、舊客戶、其他

#### 轉介單位
- 轉介單位、轉介單位部門

#### 需求者資訊
- 需求者姓名、身份證號、性別、年齡
- 身高、體重、電話
- 完整地址（郵遞區號、縣市、區域、詳細地址）
- 居家狀況

#### 使用語言（多選）
- 國語、台語、台語略、客語、客語略
- 粵語、粵語略、英語、英語略
- 日語、日語略、原住民語、其他

#### 身體狀況
- **管路留置**：無、鼻胃管、尿管、氣切、腸造口、胃造口
- **意識狀況**：清楚、簡單字句表達、無法表達、疑似失智、輕度失智、中度失智、中重以上失智
- **疾病與手術**：自由文字輸入
- **行動能力**：可自行走、需攙扶、拐杖、助行器、輪椅、臥床
- **位移需求**：不需要、躺-坐、坐-坐、坐-站
- **行動受限原因**：自由文字輸入

#### 服務項目
- **服務員性別偏好**：男/女
- **服務時間狀況**：時間未定/時間已定
- **每日時數**：1-12小時或自訂
- **預計時段**：預設時段或自訂（24小時制）
- **每週次數**：1-9次或自訂
- **服務星期**：星期一到星期日（多選）

#### 詳細服務內容（各類別多選）

**備餐服務**
- 備餐-煮食、備餐-熱食、備餐-外購、備餐-灌食
- 協助餵食、留意並記錄飲食及營養狀況

**洗衣服務**
- 使用洗衣機清洗、更換床單/被套/個人衣物
- 清洗便漬衣物/床單/被套

**清潔服務**
- 注意居家安全、確保沐浴環境安全、浴室環境清潔
- 臥房環境清潔、客廳環境清潔、廚房環境清潔
- 膳後清潔處理、抽油煙機維持表面不油膩
- 清理一般家庭垃圾、廚餘清洗處理
- 便器(便盆、尿壺)清洗處理

**協助服務**
- 移位、陪同外出辦事/購物、代購生活物品
- 協助申辦各項福利文件、代繳各項費用

**醫療相關服務**
- 陪同就醫並撰寫就醫相關記錄、陪同/代領藥物、用藥提醒
- 依藥袋指示協助分藥、服藥、協助使用甘油球通便
- 協助使用簡便之攜帶式血糖機驗血糖、生命徵象測量

**陪伴服務**
- 居家關懷陪伴、訪友規劃與協助
- 休閒安排與協助、長者活動與協助

**身體照顧服務**
- 擦澡、洗頭、協助沐浴、協助口腔清潔
- 協助選穿衣物、整理儀容、協助修容、協助修剪指甲
- 翻身、拍背、簡易關節活動、大小便處理、協助更換尿布

**特殊服務**
- 單次洗澡、陪同就醫、外籍看護指導

#### 客戶回應（多選）
- 進一步約定訪視、請公司有人力再回覆、表示考慮後再來電
- 郵寄文宣、傳真文宣、EMail文宣
- 服務內容範圍外、表示太貴不考慮、服務區域外

#### 備註說明
- 自由文字輸入

## 🔧 技術特色

### 資料庫設計
- ✅ JSON 格式儲存多選項目
- ✅ 敏感資料加密（姓名、電話、身份證號）
- ✅ 完整的索引設計
- ✅ 軟刪除機制

### 前端功能
- ✅ 響應式設計（支援手機和桌面）
- ✅ 美觀的 UI 設計
- ✅ 表單驗證
- ✅ Ajax 動態載入
- ✅ SweetAlert2 提示訊息
- ✅ Select2 下拉選單增強

### 後端功能
- ✅ 完整的 CRUD 操作
- ✅ 搜尋和分頁
- ✅ 權限驗證
- ✅ 錯誤處理
- ✅ 向後相容（支援簡易和詳細兩種模式）

## 🚀 使用方式

### 1. 建立資料表
```sql
-- 執行 sql_schema_phone_consultation_records.sql 中的 SQL
```

### 2. 訪問系統
- **主列表頁**：`/phoneConsultationRecords`
- **簡易新增**：`/phoneConsultationRecords/create`
- **詳細新增**：`/phoneConsultationRecords/create-detailed`
- **編輯記錄**：`/phoneConsultationRecords/edit/:id`

### 3. 功能操作
- 📝 **新增記錄**：選擇簡易版或詳細版表單
- 🔍 **搜尋記錄**：使用多種搜尋條件
- ✏️ **編輯記錄**：點擊記錄卡片中的編輯按鈕
- 🗑️ **刪除記錄**：軟刪除機制，可恢復

## 🔒 安全特性

- ✅ 敏感資料加密（來電者姓名、電話、身份證號）
- ✅ 權限驗證中間件
- ✅ SQL 注入防護
- ✅ 軟刪除機制
- ✅ 操作日誌記錄

## 📈 系統優勢

1. **完整性**：涵蓋 A4.txt 表單的所有欄位
2. **靈活性**：支援簡易和詳細兩種模式
3. **可擴展性**：JSON 格式儲存，易於擴展
4. **用戶體驗**：美觀的 UI 和流暢的操作
5. **相容性**：完全模仿 homeVisitRecords 的風格
6. **安全性**：完整的安全機制

## 🎉 總結

這個電話諮詢紀錄系統完全基於您提供的 A4.txt 表單內容創建，提供了從簡易到詳細的完整功能，確保能滿足各種使用場景的需求。系統採用現代化的設計和技術，提供優秀的用戶體驗和強大的功能。
