# 主責客專欄位控制功能說明

## 🎯 功能需求

與接聽人員欄位相同，當主責客專代碼欄位有輸入時，主責客專姓名欄位應該變成 disabled（禁用），因為姓名是自動從資料庫查詢取得的，不應該手動修改。

## ✅ 實現功能

### 1. 預設狀態
- ✅ 主責客專代碼：可手動輸入
- ✅ 主責客專姓名：當有代碼時自動設為 readonly

### 2. 動態控制
- ✅ 當代碼欄位有值時 → 姓名欄位變為 readonly
- ✅ 當代碼欄位為空時 → 姓名欄位可以手動輸入
- ✅ 代碼變更時自動查詢對應姓名

### 3. 視覺回饋
- ✅ readonly 欄位有特殊樣式（灰色背景）
- ✅ 滑鼠游標顯示為 not-allowed

## 🔧 技術實現

### HTML 結構
```html
<!-- 主責客專代碼（可手動輸入） -->
<input type="text" class="form-control" id="duty_member_code">

<!-- 主責客專姓名（動態 readonly） -->
<input type="text" class="form-control" id="duty_member_name" readonly>
```

### JavaScript 邏輯

#### 1. 頁面載入時初始化
```javascript
function loadContactPersonInfo() {
    // ... 接聽人員處理邏輯 ...
    
    // 初始化主責客專姓名欄位狀態
    const dutyMemberCode = $('#duty_member_code').val().trim();
    if (dutyMemberCode) {
        $('#duty_member_name').prop('readonly', true);
    } else {
        $('#duty_member_name').prop('readonly', false);
    }
}
```

#### 2. 主責客專代碼變更監聽
```javascript
$('#duty_member_code').on('input', function() {
    const codeValue = $(this).val().trim();
    const nameField = $('#duty_member_name');
    
    if (codeValue) {
        // 有代碼 → readonly + 自動查詢
        nameField.prop('readonly', true);
        
        // AJAX 查詢姓名
        $.ajax({
            type: 'POST',
            url: '/phoneConsultationRecords/get-contact-person-info',
            contentType: 'application/json',
            data: JSON.stringify({ contact_person_code: codeValue }),
            success: function (data) {
                if (data.state === "success" && data.data) {
                    nameField.val(data.data.name);
                } else {
                    nameField.val('');
                }
            },
            error: function () {
                nameField.val('');
                console.log('無法取得主責客專資訊');
            }
        });
    } else {
        // 無代碼 → 允許手動輸入
        nameField.prop('readonly', false).val('');
    }
});
```

## 🎨 用戶體驗

### 正常流程
1. **輸入代碼** → 主責客專代碼欄位輸入人員代碼
2. **自動查詢** → 系統自動查詢對應的姓名
3. **填入姓名** → 姓名自動填入並變為 readonly
4. **視覺提示** → readonly 欄位有灰色背景，清楚表示不可編輯

### 異常處理
1. **查詢失敗** → 姓名欄位清空，但保持 readonly 狀態
2. **無代碼** → 姓名欄位可編輯，支援手動輸入
3. **代碼清空** → 姓名欄位變為可編輯，內容清空

## 📋 適用範圍

### 詳細表單 (`detailed_record.ejs`)
- ✅ 主責客專代碼欄位控制
- ✅ 主責客專姓名欄位動態 readonly

### 簡易表單 (`record.ejs`)
- ❌ 不適用（簡易表單沒有主責客專欄位）

## 🔄 狀態轉換

```
頁面載入
    ↓
檢查主責客專代碼欄位
    ↓
有代碼？
├─ 是 → 設定 readonly
└─ 否 → 允許手動輸入

主責客專代碼欄位變更
    ↓
有輸入值？
├─ 是 → 設定 readonly → AJAX 查詢姓名 → 填入姓名
└─ 否 → 移除 readonly → 清空姓名
```

## 🔗 與接聽人員欄位的一致性

### 相同的控制邏輯
- ✅ 代碼有值 → 姓名 readonly
- ✅ 代碼為空 → 姓名可編輯
- ✅ 相同的 CSS 樣式
- ✅ 相同的 AJAX API 端點

### 使用相同的 API
- ✅ 使用 `/phoneConsultationRecords/get-contact-person-info`
- ✅ 傳入 `contact_person_code` 參數
- ✅ 回傳相同格式的資料

## 🛡️ 資料完整性

### 確保資料準確性
- ✅ 自動查詢避免手動輸入錯誤
- ✅ readonly 防止意外修改
- ✅ 代碼與姓名的一致性

### 容錯機制
- ✅ 查詢失敗時的錯誤處理
- ✅ 無代碼時支援完全手動操作
- ✅ 錯誤提示和日誌記錄

## 🎯 優點總結

1. **一致性** - 與接聽人員欄位控制邏輯完全一致
2. **自動化** - 減少手動輸入，提升效率
3. **準確性** - 避免姓名輸入錯誤
4. **用戶友善** - 清楚的視覺提示和狀態控制
5. **可維護性** - 重用現有的 API 和邏輯

## 📝 注意事項

1. **API 重用** - 主責客專和接聽人員使用相同的查詢 API
2. **欄位區別** - 雖然邏輯相同，但要注意欄位 ID 的區別
3. **錯誤訊息** - 為主責客專提供專門的錯誤訊息
4. **初始化** - 頁面載入時要檢查現有的代碼值

這個功能確保了主責客專資訊的準確性和一致性，與接聽人員欄位提供相同的用戶體驗。
