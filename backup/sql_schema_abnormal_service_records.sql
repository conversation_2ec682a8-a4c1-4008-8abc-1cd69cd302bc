-- 異常服務記錄表格 (D23)
CREATE TABLE IF NOT EXISTS `cs_abnormal_service_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `manage_oid` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  `project_id` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊區域
  `service_no` varchar(50) DEFAULT NULL COMMENT '表單編號',
  `service_date` date DEFAULT NULL COMMENT '服務日期',
  `service_time_start` time DEFAULT NULL COMMENT '開始時間',
  `service_time_end` time DEFAULT NULL COMMENT '結束時間',
  `gov_member` varchar(100) DEFAULT NULL COMMENT '主責照專',
  `id_number` varchar(20) DEFAULT NULL COMMENT '使用者身分證',
  `user_name` varchar(100) DEFAULT NULL COMMENT '使用者姓名',
  `duty_member_code` varchar(50) DEFAULT NULL COMMENT '社工/督導代碼',
  `duty_member_name` varchar(100) DEFAULT NULL COMMENT '社工/督導姓名',
  `service_type` varchar(20) DEFAULT NULL COMMENT '服務類別',
  `event_targets` text DEFAULT NULL COMMENT '事件對象(JSON格式)',
  
  -- 當事人資料
  `party_id_number` varchar(20) DEFAULT NULL COMMENT '當事人身分證',
  `party_user_name` varchar(100) DEFAULT NULL COMMENT '當事人姓名',
  `party_srv_member_code` varchar(50) DEFAULT NULL COMMENT '當事人服務人員代碼',
  `party_srv_member_name` varchar(100) DEFAULT NULL COMMENT '當事人服務人員姓名',
  `party_other` varchar(500) DEFAULT NULL COMMENT '當事人其他資訊',
  `party_same_as_user` tinyint(1) DEFAULT 0 COMMENT '當事人是否同上',
  
  -- 事件對象資料
  `target_id_number` varchar(20) DEFAULT NULL COMMENT '事件對象身分證',
  `target_user_name` varchar(100) DEFAULT NULL COMMENT '事件對象姓名',
  `target_srv_member_code` varchar(50) DEFAULT NULL COMMENT '事件對象服務人員代碼',
  `target_srv_member_name` varchar(100) DEFAULT NULL COMMENT '事件對象服務人員姓名',
  `target_other` varchar(500) DEFAULT NULL COMMENT '事件對象其他資訊',
  `target_same_as_user` tinyint(1) DEFAULT 0 COMMENT '事件對象是否同上',
  
  -- 事件內容
  `report_method` varchar(20) DEFAULT NULL COMMENT '通報方式',
  `report_method_other` varchar(100) DEFAULT NULL COMMENT '其他通報方式說明',
  `incident_reasons` text DEFAULT NULL COMMENT '事由(JSON格式)',
  `incident_reasons_other` varchar(500) DEFAULT NULL COMMENT '其他事由說明',
  `incident_content` text DEFAULT NULL COMMENT '內容/過程',
  `handling_result` text DEFAULT NULL COMMENT '處理結果',
  
  -- 狀態與時間戳記
  `status` varchar(20) DEFAULT '待處理' COMMENT '處理狀態',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  `deleted_by` int(11) DEFAULT NULL COMMENT '刪除者ID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (`id`),
  KEY `idx_manage_oid` (`manage_oid`),
  KEY `idx_service_date` (`service_date`),
  KEY `idx_id_number` (`id_number`),
  KEY `idx_status` (`status`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='異常服務記錄表';

-- 插入測試資料
INSERT INTO `cs_abnormal_service_records` (
  `manage_oid`, `project_id`, `created_by`,
  `service_no`, `service_date`, `service_time_start`, `service_time_end`,
  `gov_member`, `id_number`, `user_name`,
  `duty_member_code`, `duty_member_name`,
  `service_type`, `event_targets`,
  `party_id_number`, `party_user_name`, `party_same_as_user`,
  `report_method`, `incident_reasons`, `incident_content`, `handling_result`,
  `status`
) VALUES (
  1, 1, 1,
  'ASR20250702001', '2025-07-02', '09:00:00', '10:00:00',
  '李照專', 'A123456789', '王小明',
  'B1234', '李庭怡',
  '照服', '["本人", "家屬"]',
  'A123456789', '王小明', 1,
  '電話', '["照顧員照顧技術未符期待。請於\"其他\"說明何種技術項目"]',
  '客戶反映照顧員在協助洗澡時技術不熟練，造成客戶不適。',
  '已安排重新訓練照顧員，並派遣資深照顧員協助指導。',
  '已處理'
);
