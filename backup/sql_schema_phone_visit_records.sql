-- 電訪紀錄表格 (D22)
CREATE TABLE IF NOT EXISTS `cs_phone_visit_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `manage_oid` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  `project_id` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  `created_by` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊區域 (黃色背景)
  `case_number` varchar(50) DEFAULT NULL COMMENT '個案編號',
  `id_number` varchar(20) DEFAULT NULL COMMENT '身分證',
  `phone` varchar(20) DEFAULT NULL COMMENT '電話',
  `create_date` date DEFAULT NULL COMMENT '建檔日期',
  `modify_date` date DEFAULT NULL COMMENT '修改日期',
  `call_time` time DEFAULT NULL COMMENT '來電時間',
  
  -- 執行方式區域 (綠色背景)
  `execution_method` varchar(20) DEFAULT '電話' COMMENT '執行方式',
  `call_target` varchar(20) DEFAULT NULL COMMENT '對象 (本人/家屬/機構)',
  `contact_person_code` varchar(50) DEFAULT NULL COMMENT '接聽人員代碼',
  `contact_person_name` varchar(100) DEFAULT NULL COMMENT '接聽人員姓名',
  
  -- 來電原因 (多選)
  `call_reasons` text DEFAULT NULL COMMENT '來電原因(JSON格式)',
  `call_reason_other` varchar(500) DEFAULT NULL COMMENT '其他來電原因',
  
  -- 其他需求 (多選)
  `other_needs` text DEFAULT NULL COMMENT '其他需求(JSON格式)',
  `other_needs_description` varchar(500) DEFAULT NULL COMMENT '其他需求說明',
  
  -- 內容摘要
  `content_summary` text DEFAULT NULL COMMENT '內容摘要',
  
  -- 意見紀錄
  `opinion_record` text DEFAULT NULL COMMENT '意見紀錄',
  
  -- 後續處理 (多選)
  `follow_up_actions` text DEFAULT NULL COMMENT '後續處理(JSON格式)',
  `follow_up_other` varchar(500) DEFAULT NULL COMMENT '其他後續處理',
  `follow_up_description` text DEFAULT NULL COMMENT '後續處理說明',
  
  -- 狀態與時間戳記
  `status` varchar(20) DEFAULT '待處理' COMMENT '處理狀態',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  `deleted_by` int(11) DEFAULT NULL COMMENT '刪除者ID',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (`id`),
  KEY `idx_manage_oid` (`manage_oid`),
  KEY `idx_case_number` (`case_number`),
  KEY `idx_phone` (`phone`),
  KEY `idx_create_date` (`create_date`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_contact_person_code` (`contact_person_code`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='電訪紀錄表 (D22)';

-- 插入範例資料
INSERT INTO `cs_phone_visit_records` (
  `manage_oid`, `project_id`, `created_by`, `case_number`, `id_number`, `phone`,
  `create_date`, `modify_date`, `call_time`, `execution_method`, `call_target`,
  `contact_person_code`, `contact_person_name`, `call_reasons`, `call_reason_other`, 
  `other_needs`, `other_needs_description`, `content_summary`, `opinion_record`, 
  `follow_up_actions`, `follow_up_other`, `follow_up_description`, `status`
) VALUES 
(1, 1, 1, '1140702', 'A123456789', '02-12345678', 
 '2025-01-02', '2025-01-02', '09:30:00', '電話', '本人',
 'ST001', '張社工', '["詢問服務內容與收費標準", "申請居家照護服務"]', '需要了解更多細節',
 '["轉介服務"]', '希望能提供相關資源', 
 '案主詢問居家照護服務的相關資訊，包括收費標準和服務內容。已提供基本說明並安排後續訪視。',
 '案主表示對服務內容滿意，希望能盡快安排服務開始。',
 '["轉知督導", "電話追蹤"]', '需要安排實地評估', 
 '已轉知督導，預計一週內安排實地評估，並於三天後電話追蹤案主意願。',
 '處理中'),

(1, 1, 1, '1140703', 'B987654321', '02-87654321',
 '2025-01-02', '2025-01-02', '14:15:00', '電話', '家屬',
 'ST002', '李社工', '["詢問照護員之照護品質與服務"]', '',
 '["居家照護服務收費標準"]', '',
 '家屬反映照護員服務態度良好，但希望了解是否可以調整服務時間。',
 '家屬對目前服務整體滿意，建議維持現有服務品質。',
 '["通知照護員", "內部處理"]', '',
 '已通知照護員家屬的建議，並調整服務時間安排。',
 '已完成'),

(1, 1, 1, '1140704', 'C111222333', '0912-345678',
 '2025-01-02', '2025-01-02', '16:45:00', '電話', '機構',
 'ST003', '王社工', '["轉介"]', '醫院轉介個案',
 '["轉介服務"]', '需要長期照護服務',
 '醫院轉介一位需要長期照護的個案，已初步了解個案狀況和需求。',
 '個案狀況穩定，適合居家照護服務，建議盡快安排評估。',
 '["轉知督導", "轉介服務"]', '安排評估小組',
 '已安排評估小組於明日前往個案住所進行評估，並準備相關服務計畫。',
 '待處理');
