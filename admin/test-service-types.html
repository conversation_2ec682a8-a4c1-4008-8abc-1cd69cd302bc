<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試服務類別 API</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>測試服務類別 API</h1>
    
    <div>
        <h2>服務類別選單測試</h2>
        <select id="service_type" name="service_type">
            <option value="">請選擇</option>
        </select>
    </div>
    
    <div>
        <h2>API 回應</h2>
        <pre id="api-response"></pre>
    </div>

    <script>
        $(document).ready(function() {
            loadServiceTypes();
        });

        function loadServiceTypes() {
            $.ajax({
                url: '/everyDailyServiceRecords/api/system-codes/SRVTYPE',
                method: 'GET',
                success: function(response) {
                    console.log('API 回應:', response);
                    $('#api-response').text(JSON.stringify(response, null, 2));
                    
                    if (response.state === 'success') {
                        const serviceTypeSelect = $('#service_type');
                        // 清空現有選項（保留預設選項）
                        serviceTypeSelect.find('option:not(:first)').remove();
                        
                        // 添加從資料庫載入的選項
                        response.data.forEach(function(item) {
                            serviceTypeSelect.append(
                                $('<option></option>')
                                    .attr('value', item.code_id)
                                    .text(item.code_name)
                            );
                        });
                    } else {
                        console.error('載入服務類別失敗:', response.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('載入服務類別錯誤:', error);
                    $('#api-response').text('錯誤: ' + error);
                }
            });
        }
    </script>
</body>
</html>
