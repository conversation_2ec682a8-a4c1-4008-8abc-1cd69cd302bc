<%- include('../partials/header') %>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
    :root {
        --primary-color: #007bff;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --hover-color: #f5f5f5;
    }

    .page-container {
        padding: 2rem;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    /* .page-header {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 4px solid var(--primary-color);
    }

    .page-title {
        color: var(--dark-color);
        font-size: 2rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    } */

     .page-header {
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-align: center;
        color: #333;
    }
    
    .page-title i {
        color: var(--primary-color);
    }

    .form-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        display: block;
    }

    .required {
        color: var(--danger-color);
        margin-left: 0.25rem;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background-color: white;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-control:read-only {
        background-color: #CCCCCC;
        color: #000000;
    }

    .input-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .radio-group, .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .radio-item, .checkbox-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .radio-item input[type="radio"],
    .checkbox-item input[type="checkbox"] {
        margin: 0;
        transform: scale(1.2);
    }

    .radio-item label,
    .checkbox-item label {
        margin: 0;
        cursor: pointer;
        font-weight: normal;
    }

    .fieldset-custom {
        border: 2px solid var(--border-color);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .legend-custom {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 1.1rem;
        padding: 0 1rem;
        margin-bottom: 1rem;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-left: -15px;
        margin-right: -15px;
    }

    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
        padding-left: 15px;
        padding-right: 15px;
    }

    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding-left: 15px;
        padding-right: 15px;
    }

    .col-md-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
        padding-left: 15px;
        padding-right: 15px;
    }

    .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }

    @media (max-width: 768px) {
        .col-md-6, .col-md-4, .col-md-8 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    .btn-group-custom {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 2rem;
        padding: 2rem;
        background-color: var(--light-color);
        border-radius: 8px;
    }

    .btn-custom {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        min-width: 120px;
        justify-content: center;
    }

    .btn-save {
        background: linear-gradient(135deg, var(--success-color), #218838);
        color: white;
    }

    .btn-save:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .btn-cancel {
        background: linear-gradient(135deg, var(--secondary-color), #5a6268);
        color: white;
    }

    .btn-cancel:hover {
        background: linear-gradient(135deg, #5a6268, #495057);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    .textarea-large {
        min-height: 120px;
        resize: vertical;
    }

    .checkbox-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .other-input {
        margin-top: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .other-input label {
        font-weight: 600;
        margin-right: 0.5rem;
        white-space: nowrap;
    }

    .other-input input {
        flex: 1;
    }

    .hint-text {
        font-size: 0.8rem;
        color: var(--secondary-color);
        margin-top: 0.25rem;
        font-style: italic;
    }

    @media (max-width: 768px) {
        .page-container {
            padding: 1rem;
        }

        .radio-group, .checkbox-group {
            flex-direction: column;
            gap: 0.5rem;
        }

        .btn-group-custom {
            flex-direction: column;
        }
    }
</style>

<div class="page-container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-calendar-day"></i>
            <%= mode === 'create' ? '新增日常服務記錄' : '編輯日常服務記錄' %>
        </h1>
    </div>

    <form id="recordForm">
        <% if (mode === 'edit' && record) { %>
            <input type="hidden" id="record_id" name="id" value="<%= record.id %>">
        <% } %>

        <!-- 基本資訊卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i>
                基本資訊
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">表單編號</label>
                            <input type="text" class="form-control" id="service_no" name="service_no" 
                                   value="<%= mode === 'edit' && record ? record.service_no : '' %>" readonly>
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <div class="form-group">
                            <label class="form-label">
                                日期/時間 <span class="required">*</span>
                            </label>
                            <div class="input-group">
                                <input type="date" class="form-control" id="service_date" name="service_date" 
                                       value="<%= mode === 'edit' && record ? record.service_date : '' %>" required style="width: 30%;">
                                <input type="time" class="form-control" id="service_time_start" name="service_time_start" 
                                       value="<%= mode === 'edit' && record ? record.service_time_start : '' %>" style="width: 25%;">
                                <span style="margin: 0 10px; align-self: center;">至</span>
                                <input type="time" class="form-control" id="service_time_end" name="service_time_end" 
                                       value="<%= mode === 'edit' && record ? record.service_time_end : '' %>" style="width: 25%;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">進行方式</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="method_phone" name="service_method" value="電話"
                                           <%= mode === 'edit' && record && record.service_method === '電話' ? 'checked' : '' %>>
                                    <label for="method_phone">電話</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="method_visit" name="service_method" value="訪視"
                                           <%= mode === 'edit' && record && record.service_method === '訪視' ? 'checked' : '' %>>
                                    <label for="method_visit">訪視</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="method_other" name="service_method" value="其他"
                                           <%= mode === 'edit' && record && record.service_method === '其他' ? 'checked' : '' %>>
                                    <label for="method_other">其他</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用者資訊卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-user"></i>
                使用者資訊
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                使用者 <span class="required">*</span>
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="id_number" name="id_number"
                                       placeholder="身分證號" style="width: 45%;" required
                                       value="<%= mode === 'edit' && record ? record.id_number : '' %>">
                                <input type="text" class="form-control" id="user_name" name="user_name"
                                       readonly style="width: 45%; margin-left: 10px; background-color: #CCCCCC;"
                                       value="<%= mode === 'edit' && record ? record.user_name : '' %>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">社工/督導</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="duty_member_code" name="duty_member_code"
                                       placeholder="代碼" style="width: 45%;"
                                       value="<%= mode === 'edit' && record ? record.duty_member_code : '' %>">
                                <input type="text" class="form-control" id="duty_member_name" name="duty_member_name"
                                       readonly style="width: 45%; margin-left: 10px; background-color: #CCCCCC;"
                                       value="<%= mode === 'edit' && record ? record.duty_member_name : '' %>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">服務類別</label>
                            <select class="form-select" id="service_type" name="service_type">
                                <option value="">(全部)</option>
                                <option value="照服" <%= mode === 'edit' && record && record.service_type === '照服' ? 'selected' : '' %>>照服</option>
                                <option value="日照" <%= mode === 'edit' && record && record.service_type === '日照' ? 'selected' : '' %>>日照</option>
                                <option value="短照" <%= mode === 'edit' && record && record.service_type === '短照' ? 'selected' : '' %>>短照</option>
                                <option value="喘息" <%= mode === 'edit' && record && record.service_type === '喘息' ? 'selected' : '' %>>喘息</option>
                                <option value="自費方案" <%= mode === 'edit' && record && record.service_type === '自費方案' ? 'selected' : '' %>>自費方案</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 對象資訊卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-users"></i>
                對象資訊
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">對象（可複選）</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="target_self" name="targets" value="本人">
                            <label for="target_self">本人</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="target_family" name="targets" value="家屬">
                            <label for="target_family">家屬</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="target_service" name="targets" value="服務員">
                            <label for="target_service">服務員</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="target_institution" name="targets" value="機構">
                            <label for="target_institution">機構</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="target_other" name="targets" value="其他">
                            <label for="target_other">其他</label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">服務人員</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="srv_member_code" name="srv_member_code"
                                       placeholder="代碼" style="width: 45%;"
                                       value="<%= mode === 'edit' && record ? record.srv_member_code : '' %>">
                                <input type="text" class="form-control" id="srv_member_name" name="srv_member_name"
                                       readonly style="width: 45%; margin-left: 10px; background-color: #CCCCCC;"
                                       value="<%= mode === 'edit' && record ? record.srv_member_name : '' %>">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">家屬</label>
                            <input type="text" class="form-control" id="family" name="family"
                                   placeholder="家屬姓名"
                                   value="<%= mode === 'edit' && record ? record.family : '' %>">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">其他人員</label>
                    <input type="text" class="form-control" id="other_people" name="other_people"
                           placeholder="其他參與人員"
                           value="<%= mode === 'edit' && record ? record.other_people : '' %>">
                </div>
            </div>
        </div>

        <!-- 事由內容卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-file-alt"></i>
                事由內容
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label class="form-label">事由選項（可複選）</label>
                    <div class="checkbox-grid">
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_1" name="service_items" value="與案家討論服務內容調整">
                            <label for="item_1">與案家討論服務內容調整</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_2" name="service_items" value="接受申訴">
                            <label for="item_2">接受申訴</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_3" name="service_items" value="照會或連結至服務提供單位">
                            <label for="item_3">照會或連結至服務提供單位</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_4" name="service_items" value="與案家討論照顧計畫">
                            <label for="item_4">與案家討論照顧計畫</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_5" name="service_items" value="協助申請相關福利資源">
                            <label for="item_5">協助申請相關福利資源</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_6" name="service_items" value="提供諮詢服務">
                            <label for="item_6">提供諮詢服務</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_7" name="service_items" value="協助轉介其他服務">
                            <label for="item_7">協助轉介其他服務</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_8" name="service_items" value="定期追蹤服務狀況">
                            <label for="item_8">定期追蹤服務狀況</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="item_other" name="service_items" value="其他">
                            <label for="item_other">其他</label>
                        </div>
                    </div>
                    <div class="other-input">
                        <label>其他事由：</label>
                        <input type="text" class="form-control" id="service_item_other" name="service_item_other"
                               placeholder="請說明其他事由"
                               value="<%= mode === 'edit' && record ? record.service_item_other : '' %>">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">其他需求</label>
                    <input type="text" class="form-control" id="other_requirements" name="other_requirements"
                           placeholder="請描述其他需求"
                           value="<%= mode === 'edit' && record ? record.other_requirements : '' %>">
                </div>

                <div class="form-group">
                    <label class="form-label">內容說明</label>
                    <textarea class="form-control textarea-large" id="service_description" name="service_description"
                              placeholder="請詳細描述服務內容和過程"><%= mode === 'edit' && record ? record.service_description : '' %></textarea>
                </div>
            </div>
        </div>

        <!-- 完成日期卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-calendar-check"></i>
                完成日期
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">應完成日</label>
                            <input type="date" class="form-control" id="expected_completion_date" name="expected_completion_date"
                                   value="<%= mode === 'edit' && record ? record.expected_completion_date : '' %>">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">實完成日</label>
                            <input type="date" class="form-control" id="actual_completion_date" name="actual_completion_date"
                                   value="<%= mode === 'edit' && record ? record.actual_completion_date : '' %>">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 審核設定卡片 -->
        <div class="form-card">
            <div class="card-header">
                <i class="fas fa-cog"></i>
                審核設定
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">審核狀態</label>
                            <select class="form-select" id="review_status" name="review_status">
                                <option value="未審核" <%= mode === 'edit' && record && record.review_status === '未審核' ? 'selected' : '' %>>未審核</option>
                                <option value="未審核-尚無建議" <%= mode === 'edit' && record && record.review_status === '未審核-尚無建議' ? 'selected' : '' %>>未審核-尚無建議</option>
                                <option value="未審核-已有建議" <%= mode === 'edit' && record && record.review_status === '未審核-已有建議' ? 'selected' : '' %>>未審核-已有建議</option>
                                <option value="未審核-已發送通知" <%= mode === 'edit' && record && record.review_status === '未審核-已發送通知' ? 'selected' : '' %>>未審核-已發送通知</option>
                                <option value="已審核" <%= mode === 'edit' && record && record.review_status === '已審核' ? 'selected' : '' %>>已審核</option>
                                <option value="已審核-尚無建議" <%= mode === 'edit' && record && record.review_status === '已審核-尚無建議' ? 'selected' : '' %>>已審核-尚無建議</option>
                                <option value="已審核-已有建議" <%= mode === 'edit' && record && record.review_status === '已審核-已有建議' ? 'selected' : '' %>>已審核-已有建議</option>
                                <option value="有附件" <%= mode === 'edit' && record && record.review_status === '有附件' ? 'selected' : '' %>>有附件</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">拋轉設定</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="upload_no" name="upload_setting" value="不拋轉"
                                           <%= mode === 'edit' && record && record.upload_setting === '不拋轉' ? 'checked' : 'checked' %>>
                                    <label for="upload_no">不拋轉</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="upload_yes" name="upload_setting" value="要拋轉"
                                           <%= mode === 'edit' && record && record.upload_setting === '要拋轉' ? 'checked' : '' %>>
                                    <label for="upload_yes">要拋轉</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="btn-group-custom">
            <button type="submit" class="btn-custom btn-save">
                <i class="fas fa-save"></i>
                儲存
            </button>
            <a href="/everyDailyServiceRecords" class="btn-custom btn-cancel">
                <i class="fas fa-times"></i>
                取消
            </a>
        </div>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="module">
    import userLookupModule from '/services/userLookupModule.js';
    window.userModuleInstance = userLookupModule();
    window.srvModuleInstance = userLookupModule();
</script>
<script>
    $(document).ready(function() {
        getOrganizations();
        showFunctionElements("D-1-EDIT");
        // 如果是新增模式，生成表單編號
        if (!$('#record_id').val()) {
            generateServiceNo();
        }

        // 設定預設日期為今天
        if (!$('#service_date').val()) {
            const today = new Date().toISOString().split('T')[0];
            $('#service_date').val(today);
        }

        // 載入編輯模式的資料
        if ($('#record_id').val()) {
            loadEditData();
        }

        // 表單提交
        $('#recordForm').submit(function(e) {
            e.preventDefault();
            saveRecord();
        });
    });

    // 生成表單編號
    function generateServiceNo() {
        $.ajax({
            url: '/everyDailyServiceRecords/generate-service-no',
            method: 'GET',
            success: function(response) {
                if (response.state === 'success') {
                    $('#service_no').val(response.serviceNo);
                }
            },
            error: function() {
                console.error('生成表單編號失敗');
            }
        });
    }

    // 儲存記錄
    function saveRecord() {
        // 收集表單資料
        const formData = {
            id: $('#record_id').val() || null,
            company_id: $('#company_id').val(),
            id_number: $('#id_number').val(),
            user_name: $('#user_name').val(),
            srv_member: $('#srv_member').val(),
            srv_member_name: $('#srv_member_name').val(),
            service_date: $('#service_date').val(),
            time1: $('#time1').val(),
            time2: $('#time2').val(),
            service_hour: $('#service_hour').val(),
            service_min: $('#service_min').val(),
            traffic_min: $('#traffic_min').val(),
            service_type: $('#service_type').val(),
            srv_item: $('#srv_item').val(),
            service_status: $('input[name="service_status"]:checked').val(),
            traffic_times: $('input[name="traffic_times"]:checked').val(),
            remark: $('#remark').val()
        };

        // 驗證必填欄位
        if (!formData.service_date) {
            showError('請選擇服務日期');
            return;
        }

        if (!formData.id_number) {
            showError('請輸入使用者身分證號');
            return;
        }

        if (!formData.srv_member) {
            showError('請輸入服務人員代碼');
            return;
        }

        if (!formData.time1 || !formData.time2) {
            showError('請填寫完整的服務時段');
            return;
        }

        if (!formData.service_type) {
            showError('請選擇服務類別');
            return;
        }

        // 發送請求
        $.ajax({
            url: '/everyDailyServiceRecords/save-record',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.state === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: '儲存成功',
                        text: '每日服務記錄已成功儲存',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        window.location.href = '/everyDailyServiceRecords';
                    });
                } else {
                    showError(response.msg || '儲存失敗');
                }
            },
            error: function(xhr, status, error) {
                console.error('儲存錯誤:', error);
                showError('儲存時發生錯誤: ' + error);
            }
        });
    }

    // 顯示錯誤訊息
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: '錯誤',
            text: message
        });
    }
</script>

<%- include('../partials/footer') %>
