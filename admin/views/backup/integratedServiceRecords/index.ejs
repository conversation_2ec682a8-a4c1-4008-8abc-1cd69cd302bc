<%- include('../partials/header') %>
<%- include('../partials/sidebar') %>

<style>
    .search-form {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 0.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.25rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        font-size: 0.9rem;
    }

    .btn-group {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.25rem;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #0056b3, #003d80);
        transform: translateY(-2px);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #495057, #343a40);
        transform: translateY(-2px);
    }

    .records-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
        overflow-x: auto;
    }

    .records-table th, .records-table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        text-align: left;
    }

    .records-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .records-table tbody tr:hover {
        background-color: #f1f1f1;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.2rem;
    }

    .btn-view {
        background: linear-gradient(135deg, var(--info-color), #138496);
        color: white;
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }

    .pagination-item {
        padding: 0.5rem 0.75rem;
        margin: 0 0.25rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .pagination-item:hover {
        background-color: #e9ecef;
    }

    .pagination-item.active {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .no-data {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }

    .no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .search-form {
            grid-template-columns: 1fr;
        }
        
        .records-table {
            display: block;
            overflow-x: auto;
        }
    }
</style>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">整合服務記錄查詢</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首頁</a></li>
                        <li class="breadcrumb-item active">整合服務記錄查詢</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <!-- 搜尋表單 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-search"></i>
                    搜尋條件
                </div>
                <div class="card-body">
                    <form id="searchForm">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">所屬單位</label>
                                <select class="form-select" id="company_id" name="company_id">
                                    <option value="(全部)">(全部)</option>
                                    <option value="B1690">新北服務站</option>
                                    <option value="B1691">台北服務站</option>
                                    <option value="B1692">臺中服務站</option>
                                    <option value="B1693">高雄服務站</option>
                                    <option value="B1694">桃園服務站</option>
                                    <option value="B1695">台南服務站</option>
                                    <option value="B169A">暖時光日照(台北)</option>
                                    <option value="B169B">暖時光日照(土城)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">使用者ID</label>
                                <input type="text" class="form-control" id="user_id" name="user_id" placeholder="請輸入使用者ID">
                            </div>

                            <div class="form-group">
                                <label class="form-label">服務年月</label>
                                <input type="month" class="form-control" id="service_year_month" name="service_year_month">
                            </div>

                            <div class="form-group">
                                <label class="form-label">服務日期範圍</label>
                                <div style="display: flex; gap: 0.5rem; align-items: center;">
                                    <input type="date" class="form-control" id="service_date_start" name="service_date_start">
                                    <span>至</span>
                                    <input type="date" class="form-control" id="service_date_end" name="service_date_end">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">服務人員代碼</label>
                                <input type="text" class="form-control" id="srv_member_code" name="srv_member_code" placeholder="請輸入服務人員代碼">
                            </div>

                            <div class="form-group">
                                <label class="form-label">服務類別</label>
                                <select class="form-select" id="service_type" name="service_type">
                                    <option value="(全部)">(全部)</option>
                                    <option value="5">照服</option>
                                    <option value="6">日照</option>
                                    <option value="4">短照</option>
                                    <option value="7">喘息</option>
                                    <option value="S">自費方案</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">狀態</label>
                                <select class="form-select" id="service_status" name="service_status">
                                    <option value="(全部)">(全部)</option>
                                    <option value="N">正常</option>
                                    <option value="S">自費</option>
                                    <option value="M">未遇</option>
                                    <option value="Z">預定</option>
                                    <option value="C">取消</option>
                                    <option value="R">休息</option>
                                </select>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" id="searchBtn">
                                <i class="fas fa-search"></i> 查詢
                            </button>
                            <button type="button" class="btn btn-secondary" id="resetBtn">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 查詢結果 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-table"></i>
                    查詢結果
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="records-table" id="recordsTable">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">檢視</th>
                                    <th style="width: 60px;">狀態</th>
                                    <th style="width: 80px;">案號</th>
                                    <th style="width: 100px;">姓名</th>
                                    <th style="width: 80px;">類別</th>
                                    <th style="width: 100px;">服務日期</th>
                                    <th style="width: 100px;">服務人員</th>
                                    <th style="width: 120px;">服務時段</th>
                                    <th style="width: 80px;">服務時數(小時)</th>
                                    <th style="width: 80px;">服務時數(分鐘)</th>
                                    <th style="width: 80px;">轉場交通(分鐘)</th>
                                    <th style="width: 80px;">累計排班(小時)</th>
                                    <th style="width: 60px;">是否不轉場</th>
                                    <th style="width: 150px;">服務項目/備註</th>
                                    <th style="width: 80px;">服務員體溫</th>
                                    <th style="width: 80px;">個案體溫</th>
                                    <th style="width: 60px;">血壓(高)</th>
                                    <th style="width: 60px;">血壓(低)</th>
                                    <th style="width: 60px;">脈搏</th>
                                    <th style="width: 60px;">呼吸</th>
                                    <th style="width: 80px;">通訊區域</th>
                                    <th style="width: 80px;">日間18點前</th>
                                    <th style="width: 80px;">夜間18點後</th>
                                    <th style="width: 120px;">建檔</th>
                                    <th style="width: 120px;">修改</th>
                                    <th style="width: 100px;">審核日</th>
                                </tr>
                            </thead>
                            <tbody id="recordsTableBody">
                                <tr>
                                    <td colspan="26" class="no-data">
                                        <i class="fas fa-inbox"></i><br>
                                        請輸入搜尋條件並點擊查詢按鈕
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分頁 -->
                    <div class="pagination" id="pagination"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        // 初始化
        let currentPage = 1;
        let totalPages = 0;
        const pageSize = 10;

        // 查詢按鈕點擊事件
        $('#searchBtn').on('click', function() {
            currentPage = 1;
            searchRecords();
        });

        // 重置按鈕點擊事件
        $('#resetBtn').on('click', function() {
            $('#searchForm')[0].reset();
            $('#recordsTableBody').html(`
                <tr>
                    <td colspan="26" class="no-data">
                        <i class="fas fa-inbox"></i><br>
                        請輸入搜尋條件並點擊查詢按鈕
                    </td>
                </tr>
            `);
            $('#pagination').empty();
        });

        // 查詢記錄
        function searchRecords() {
            const formData = {
                company_id: $('#company_id').val(),
                user_id: $('#user_id').val(),
                service_year_month: $('#service_year_month').val(),
                service_date_start: $('#service_date_start').val(),
                service_date_end: $('#service_date_end').val(),
                srv_member_code: $('#srv_member_code').val(),
                service_type: $('#service_type').val(),
                service_status: $('#service_status').val(),
                page: currentPage,
                pageSize: pageSize
            };

            // 顯示載入中
            Swal.fire({
                title: '載入中...',
                text: '正在查詢記錄',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // 發送請求
            $.ajax({
                url: '/integratedServiceRecords/search',
                method: 'POST',
                data: formData,
                success: function(response) {
                    Swal.close();

                    if (response.state === 'success') {
                        displayRecords(response.records);
                        totalPages = response.totalPages;
                        renderPagination();
                    } else {
                        showError(response.msg || '查詢失敗');
                    }
                },
                error: function(xhr, status, error) {
                    Swal.close();
                    showError('查詢時發生錯誤: ' + error);
                }
            });
        }

        // 顯示記錄
        function displayRecords(records) {
            const tbody = $('#recordsTableBody');
            tbody.empty();

            if (records.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="26" class="no-data">
                            <i class="fas fa-inbox"></i><br>
                            查無符合條件的記錄
                        </td>
                    </tr>
                `);
                return;
            }

            records.forEach(record => {
                const serviceItems = record.service_items || '';
                const remarks = record.remarks || '';
                const serviceItemsRemarks = [serviceItems, remarks].filter(item => item).join(' / ');

                const row = `
                    <tr>
                        <td>
                            <button class="btn-sm btn-view" onclick="viewRecord(${record.service_record_id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                        <td>${record.service_status || ''}</td>
                        <td>${record.id_number || ''}</td>
                        <td>${record.id_number || ''}</td>
                        <td>${record.service_type || ''}</td>
                        <td>${record.service_date_formatted || ''}</td>
                        <td>${record.srv_member || ''}</td>
                        <td>${record.service_time_range || ''}</td>
                        <td>${record.service_hour || '0'}</td>
                        <td>${record.service_min || '0'}</td>
                        <td>${record.traffic_min || '0'}</td>
                        <td>-</td>
                        <td>${record.traffic_times === 0 ? '是' : '否'}</td>
                        <td>${serviceItemsRemarks}</td>
                        <td>${record.staff_temperature || '-'}</td>
                        <td>${record.user_temperature || '-'}</td>
                        <td>${record.blood_pressure_high || '-'}</td>
                        <td>${record.blood_pressure_low || '-'}</td>
                        <td>${record.pulse || '-'}</td>
                        <td>${record.breathe || '-'}</td>
                        <td>-</td>
                        <td>${record.before_18 || '-'}</td>
                        <td>${record.after_18 || '-'}</td>
                        <td>${record.created_at_formatted || '-'}</td>
                        <td>${record.updated_at_formatted || '-'}</td>
                        <td>${record.review_date_formatted || '-'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }

        // 渲染分頁
        function renderPagination() {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) {
                return;
            }

            // 上一頁
            pagination.append(`
                <div class="pagination-item ${currentPage === 1 ? 'disabled' : ''}" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </div>
            `);

            // 頁碼
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            for (let i = startPage; i <= endPage; i++) {
                pagination.append(`
                    <div class="pagination-item ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                        ${i}
                    </div>
                `);
            }

            // 下一頁
            pagination.append(`
                <div class="pagination-item ${currentPage === totalPages ? 'disabled' : ''}" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </div>
            `);
        }

        // 切換頁碼
        window.changePage = function(page) {
            if (page < 1 || page > totalPages || page === currentPage) {
                return;
            }

            currentPage = page;
            searchRecords();
        };

        // 查看記錄詳情
        window.viewRecord = function(id) {
            window.location.href = `/integratedServiceRecords/view/${id}`;
        };

        // 顯示錯誤訊息
        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: '錯誤',
                text: message
            });
        }
    });
</script>

<%- include('../partials/footer') %>
