<%- include('../partials/header') %>
<%- include('../partials/sidebar') %>

<style>
    .detail-card {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
        padding: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header i {
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.5rem;
        background-color: white;
    }

    .detail-table {
        width: 100%;
        border-collapse: collapse;
    }

    .detail-table th, .detail-table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
    }

    .detail-table th {
        width: 200px;
        background-color: #f8f9fa;
        font-weight: 600;
        text-align: right;
    }

    .detail-table td {
        text-align: left;
    }

    .btn-group {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.25rem;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #0056b3, #003d80);
        transform: translateY(-2px);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #495057, #343a40);
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .detail-table th {
            width: 120px;
        }
    }
</style>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">查看整合服務記錄</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首頁</a></li>
                        <li class="breadcrumb-item"><a href="/integratedServiceRecords">整合服務記錄查詢</a></li>
                        <li class="breadcrumb-item active">查看記錄</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <!-- 服務基本資訊 -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-info-circle"></i>
                    服務基本資訊
                </div>
                <div class="card-body">
                    <table class="detail-table">
                        <tr>
                            <th>記錄ID</th>
                            <td><%= record.service_record_id %></td>
                            <th>所屬單位</th>
                            <td><%= record.company_id %></td>
                        </tr>
                        <tr>
                            <th>服務年月</th>
                            <td><%= record.service_year_month %></td>
                            <th>服務人員</th>
                            <td><%= record.srv_member %></td>
                        </tr>
                        <tr>
                            <th>使用者ID</th>
                            <td><%= record.id_number %></td>
                            <th>服務日期</th>
                            <td><%= record.service_date ? new Date(record.service_date).toLocaleDateString() : '' %></td>
                        </tr>
                        <tr>
                            <th>服務類別</th>
                            <td><%= record.service_type %></td>
                            <th>服務狀態</th>
                            <td><%= record.service_status %></td>
                        </tr>
                        <tr>
                            <th>服務時間</th>
                            <td colspan="3">
                                <%= record.service_start_time ? record.service_start_time.toString().substring(0, 5) : '' %> - 
                                <%= record.service_end_time ? record.service_end_time.toString().substring(0, 5) : '' %>
                            </td>
                        </tr>
                        <tr>
                            <th>服務時數</th>
                            <td><%= record.service_hour %> 小時</td>
                            <th>服務分鐘</th>
                            <td><%= record.service_min %> 分鐘</td>
                        </tr>
                        <tr>
                            <th>交通分鐘</th>
                            <td><%= record.traffic_min %> 分鐘</td>
                            <th>交通次數</th>
                            <td><%= record.traffic_times %></td>
                        </tr>
                        <tr>
                            <th>是否不轉場</th>
                            <td><%= record.traffic_times === 0 ? '是' : '否' %></td>
                            <th>日間/夜間</th>
                            <td>
                                日間18點前: <%= record.before_18 || '-' %><br>
                                夜間18點後: <%= record.after_18 || '-' %>
                            </td>
                        </tr>
                        <tr>
                            <th>服務項目</th>
                            <td colspan="3"><%= record.service_items || '-' %></td>
                        </tr>
                        <tr>
                            <th>備註說明</th>
                            <td colspan="3"><%= record.remarks || '-' %></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 生理量測資訊 -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-heartbeat"></i>
                    生理量測資訊
                </div>
                <div class="card-body">
                    <table class="detail-table">
                        <tr>
                            <th>服務員體溫</th>
                            <td><%= record.staff_temperature || '-' %></td>
                            <th>個案體溫</th>
                            <td><%= record.user_temperature || '-' %></td>
                        </tr>
                        <tr>
                            <th>血壓(高)</th>
                            <td><%= record.blood_pressure_high || '-' %></td>
                            <th>血壓(低)</th>
                            <td><%= record.blood_pressure_low || '-' %></td>
                        </tr>
                        <tr>
                            <th>脈搏</th>
                            <td><%= record.pulse || '-' %></td>
                            <th>呼吸</th>
                            <td><%= record.breathe || '-' %></td>
                        </tr>
                        <tr>
                            <th>血氧</th>
                            <td><%= record.blood_oxygen || '-' %></td>
                            <th>血糖</th>
                            <td><%= record.blood_sugar || '-' %></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 系統資訊 -->
            <div class="detail-card">
                <div class="card-header">
                    <i class="fas fa-cog"></i>
                    系統資訊
                </div>
                <div class="card-body">
                    <table class="detail-table">
                        <tr>
                            <th>建檔時間</th>
                            <td><%= record.created_at ? new Date(record.created_at).toLocaleString() : '-' %></td>
                            <th>修改時間</th>
                            <td><%= record.updated_at ? new Date(record.updated_at).toLocaleString() : '-' %></td>
                        </tr>
                        <tr>
                            <th>生理量測更新時間</th>
                            <td><%= record.physical_updated_at ? new Date(record.physical_updated_at).toLocaleString() : '-' %></td>
                            <th>審核日期</th>
                            <td><%= record.review_date ? new Date(record.review_date).toLocaleDateString() : '-' %></td>
                        </tr>
                        <tr>
                            <th>審核人員</th>
                            <td colspan="3"><%= record.review_by || '-' %></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- 按鈕 -->
            <div class="btn-group">
                <a href="/integratedServiceRecords" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
