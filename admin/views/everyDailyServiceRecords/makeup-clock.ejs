<%- include('../partials/header') %>

<style>
    .page-container {
        padding: 2rem;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .MyTable {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        width: 100%;
        border-collapse: collapse;
    }

    .MyTable td {
        padding: 12px 15px;
        border: 1px solid #dee2e6;
        vertical-align: middle;
    }

    .label-cell {
        background-color: #f8f9fa;
        font-weight: bold;
        width: 150px;
        text-align: left;
    }

    .input-OneCell {
        padding: 12px 15px;
    }

    .time-select {
        padding: 5px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        margin: 2px;
    }

    .btn-clear {
        padding: 5px 10px;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-left: 5px;
    }

    .btn-clear:hover {
        background-color: #5a6268;
    }

    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .radio-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .TextWidth80px {
        width: 80px;
        padding: 5px;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .TextWidth95percent {
        width: 95%;
        padding: 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .btn-group {
        text-align: center;
        margin-top: 30px;
        padding: 20px;
    }

    .btn {
        padding: 10px 30px;
        margin: 0 10px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .btn-save {
        background-color: #28a745;
        color: white;
    }

    .btn-save:hover {
        background-color: #218838;
    }

    .btn-cancel {
        background-color: #6c757d;
        color: white;
    }

    .btn-cancel:hover {
        background-color: #5a6268;
    }

    .Mylabel {
        font-size: 12px;
        color: #666;
    }

    .font14 {
        font-size: 14px;
    }

    @media (max-width: 768px) {
        .page-container {
            padding: 1rem;
        }
        
        .MyTable {
            font-size: 14px;
        }
        
        .label-cell {
            width: 120px;
        }
    }
</style>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">補卡作業</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首頁</a></li>
                        <li class="breadcrumb-item"><a href="/everyDailyServiceRecords">每日服務記錄</a></li>
                        <li class="breadcrumb-item active">補卡作業</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="page-container">
                <form id="makeupClockForm">
                    <input type="hidden" id="record_id" value="<%= recordId %>">
                    
                    <table class="MyTable">
                        <tbody>
                            <tr>
                                <td class="label-cell">
                                    <span>系統ID</span>
                                </td>
                                <td class="input-OneCell">
                                    <span id="data_id"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>居服員</span>
                                </td>
                                <td class="input-OneCell">
                                    <span id="srv_member" style="color: #0000CC"></span>&nbsp;<span id="srv_member_name"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>使用者</span>
                                </td>
                                <td class="input-OneCell">
                                    <span id="id_number" style="color: #0000CC"></span>&nbsp;<span id="user_name"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>服務日期</span>
                                </td>
                                <td class="input-OneCell">
                                    <span id="service_date"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>排班時間</span>
                                </td>
                                <td class="input-OneCell">
                                    <span>自</span>
                                    <span id="time1"></span>
                                    &nbsp;<span>至</span>
                                    &nbsp;<span id="time2"></span>
                                    &nbsp;<span>計</span>
                                    &nbsp;<span id="service_hour"></span>
                                    &nbsp;<span>小時</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>打卡時間</span>
                                </td>
                                <td class="input-OneCell">
                                    <span>自</span>
                                    <span id="clock_in_time"></span>&nbsp;<span>至</span>
                                    &nbsp;<span id="clock_out_time"></span>&nbsp;<span>計</span>
                                    &nbsp;<span id="actual_service_hours"></span>&nbsp;<span>小時</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>狀態</span>
                                </td>
                                <td class="input-OneCell">
                                    <span id="service_status"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>補卡時間</span>
                                </td>
                                <td class="input-OneCell">
                                    <span>上班</span>
                                    <br>
                                    <select class="time-select" id="makeup_in_hour" name="makeup_in_hour">
                                        <option value=""></option>
                                        <% for(let i = 0; i <= 23; i++) { %>
                                            <option value="<%= i.toString().padStart(2, '0') %>"><%= i.toString().padStart(2, '0') %></option>
                                        <% } %>
                                    </select>
                                    :
                                    <select class="time-select" id="makeup_in_minute" name="makeup_in_minute">
                                        <option value=""></option>
                                        <% for(let i = 0; i <= 59; i++) { %>
                                            <option value="<%= i.toString().padStart(2, '0') %>"><%= i.toString().padStart(2, '0') %></option>
                                        <% } %>
                                    </select>
                                    <button type="button" class="btn-clear" onclick="clearTime('in')">清</button>
                                    <br>
                                    <span>下班</span>
                                    <br>
                                    <select class="time-select" id="makeup_out_hour" name="makeup_out_hour">
                                        <option value=""></option>
                                        <% for(let i = 0; i <= 23; i++) { %>
                                            <option value="<%= i.toString().padStart(2, '0') %>"><%= i.toString().padStart(2, '0') %></option>
                                        <% } %>
                                    </select>
                                    :
                                    <select class="time-select" id="makeup_out_minute" name="makeup_out_minute">
                                        <option value=""></option>
                                        <% for(let i = 0; i <= 59; i++) { %>
                                            <option value="<%= i.toString().padStart(2, '0') %>"><%= i.toString().padStart(2, '0') %></option>
                                        <% } %>
                                    </select>
                                    <button type="button" class="btn-clear" onclick="clearTime('out')">清</button>
                                    <br>
                                    <span class="Mylabel font14">(清空時間，表示取消打卡)</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>共計</span>
                                </td>
                                <td class="input-OneCell">
                                    <input type="text" id="makeup_service_hour" name="makeup_service_hour" class="TextWidth80px" style="width:35px;" readonly>
                                    <span>小時，或</span>
                                    <input type="text" id="makeup_service_min" name="makeup_service_min" class="TextWidth80px" style="width:35px;" readonly>
                                    <span>分鐘</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>原因說明</span>
                                </td>
                                <td class="input-OneCell">
                                    <div class="radio-group">
                                        <div class="radio-item">
                                            <input type="radio" id="reason_1" name="makeup_reason" value="忘記打卡" checked>
                                            <label for="reason_1">忘記打卡</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="reason_2" name="makeup_reason" value="手機故障">
                                            <label for="reason_2">手機故障</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="reason_3" name="makeup_reason" value="當地無信號">
                                            <label for="reason_3">當地無信號</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="reason_4" name="makeup_reason" value="居督補卡">
                                            <label for="reason_4">居督補卡</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="reason_5" name="makeup_reason" value="系統維修">
                                            <label for="reason_5">系統維修</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="reason_6" name="makeup_reason" value="其他">
                                            <label for="reason_6">其他</label>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>其他原因</span>
                                </td>
                                <td class="input-OneCell">
                                    <input type="text" id="makeup_other_reason" name="makeup_other_reason" class="TextWidth95percent">
                                </td>
                            </tr>
                            <tr>
                                <td class="label-cell">
                                    <span>服務摘要</span>
                                </td>
                                <td class="input-OneCell">
                                    <textarea id="service_desc" name="service_desc" class="TextWidth95percent" rows="3" placeholder="請輸入服務摘要..."></textarea>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 按鈕群組 -->
                    <div class="btn-group">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save"></i> 儲存補卡
                        </button>
                        <a href="/everyDailyServiceRecords" class="btn btn-cancel">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    $(document).ready(function() {
        showFunctionElements("D-1-EDIT");
        
        // 載入記錄資料
        loadRecordData();

        // 時間變更時自動計算服務時數
        $('#makeup_in_hour, #makeup_in_minute, #makeup_out_hour, #makeup_out_minute').on('change', function() {
            calculateMakeupHours();
        });

        // 表單提交
        $('#makeupClockForm').submit(function(e) {
            e.preventDefault();
            saveMakeupClock();
        });
    });

    // 載入記錄資料
    function loadRecordData() {
        const recordId = $('#record_id').val();
        
        $.ajax({
            url: `/everyDailyServiceRecords/data/${recordId}`,
            method: 'GET',
            success: function(response) {
                if (response.state === 'success') {
                    const data = response.data;
                    console.log('載入的記錄資料:', data);
                    // 填入基本資訊
                    $('#data_id').text(data.id || '');
                    $('#srv_member').text(data.employee_number || '');
                    $('#srv_member_name').text(data.duty_member_name || '');
                    $('#id_number').text(data.puid || '');
                    $('#user_name').text(data.full_name || '');
                    $('#service_date').text(formatDate(data.service_date) || '');
                    $('#time1').text(data.time1 || '');
                    $('#time2').text(data.time2 || '');
                    $('#service_hour').text(data.service_hour || '');
                    $('#clock_in_time').text(data.clock_in_time || '');
                    $('#clock_out_time').text(data.clock_out_time || '');
                    
                    // 計算實際服務時數
                    if (data.actual_service_minutes) {
                        const hours = (data.actual_service_minutes / 60).toFixed(2);
                        $('#actual_service_hours').text(hours);
                    }
                    
                    // 顯示狀態
                    $('#service_status').text(getServiceStatusText(data.service_status));
                    
                    // 如果已有補卡資料，填入表單
                    if (data.makeup_clock_in_time) {
                        const inTime = data.makeup_clock_in_time.split(':');
                        $('#makeup_in_hour').val(inTime[0]);
                        $('#makeup_in_minute').val(inTime[1]);
                    }
                    
                    if (data.makeup_clock_out_time) {
                        const outTime = data.makeup_clock_out_time.split(':');
                        $('#makeup_out_hour').val(outTime[0]);
                        $('#makeup_out_minute').val(outTime[1]);
                    }
                    
                    if (data.makeup_reason) {
                        $(`input[name="makeup_reason"][value="${data.makeup_reason}"]`).prop('checked', true);
                    }
                    
                    if (data.makeup_other_reason) {
                        $('#makeup_other_reason').val(data.makeup_other_reason);
                    }
                    
                    // 計算補卡時數
                    calculateMakeupHours();
                } else {
                    showError('載入記錄失敗: ' + response.msg);
                }
            },
            error: function(xhr, status, error) {
                showError('載入記錄時發生錯誤: ' + error);
            }
        });
    }

    // 清空時間
    function clearTime(type) {
        if (type === 'in') {
            $('#makeup_in_hour').val('');
            $('#makeup_in_minute').val('');
        } else {
            $('#makeup_out_hour').val('');
            $('#makeup_out_minute').val('');
        }
        calculateMakeupHours();
    }

    // 計算補卡服務時數
    function calculateMakeupHours() {
        const inHour = $('#makeup_in_hour').val();
        const inMinute = $('#makeup_in_minute').val();
        const outHour = $('#makeup_out_hour').val();
        const outMinute = $('#makeup_out_minute').val();
        
        if (inHour && inMinute && outHour && outMinute) {
            const startTime = new Date(`2000-01-01 ${inHour}:${inMinute}:00`);
            const endTime = new Date(`2000-01-01 ${outHour}:${outMinute}:00`);
            
            if (endTime > startTime) {
                const diffMs = endTime - startTime;
                const diffMinutes = Math.floor(diffMs / (1000 * 60));
                const diffHours = (diffMinutes / 60).toFixed(2);
                
                $('#makeup_service_hour').val(diffHours);
                $('#makeup_service_min').val(diffMinutes);
            } else {
                $('#makeup_service_hour').val('');
                $('#makeup_service_min').val('');
            }
        } else {
            $('#makeup_service_hour').val('');
            $('#makeup_service_min').val('');
        }
    }

    // 儲存補卡資料
    function saveMakeupClock() {
        const inHour = $('#makeup_in_hour').val();
        const inMinute = $('#makeup_in_minute').val();
        const outHour = $('#makeup_out_hour').val();
        const outMinute = $('#makeup_out_minute').val();
        
        // 驗證必填欄位
        if (!$('input[name="makeup_reason"]:checked').val()) {
            showError('請選擇補卡原因');
            return;
        }

        // 收集表單資料
        const formData = {
            id: $('#record_id').val(),
            clock_in_time: (inHour && inMinute) ? `${inHour}:${inMinute}` : null,
            clock_out_time: (outHour && outMinute) ? `${outHour}:${outMinute}` : null,
            makeup_clock_reason: $('input[name="makeup_reason"]:checked').val(),
            makeup_clock_other_reason: $('#makeup_other_reason').val(),
            service_desc: $('#service_desc').val() || null
        };

        // 顯示載入中
        Swal.fire({
            title: '儲存中...',
            text: '正在更新補卡資料',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 發送請求
        $.ajax({
            url: '/everyDailyServiceRecords/save-makeup-clock',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.state === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: '儲存成功',
                        text: '補卡資料已成功更新',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        window.location.href = '/everyDailyServiceRecords';
                    });
                } else {
                    showError(response.msg || '儲存失敗');
                }
            },
            error: function(xhr, status, error) {
                showError('儲存時發生錯誤: ' + error);
            }
        });
    }

    // 格式化日期
    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const year = (date.getFullYear() - 1911).toString(); // 民國年
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}/${month}/${day}`;
    }

    // 轉換服務狀態代號為中文文字
    function getServiceStatusText(statusCode) {
        const statusMap = {
            'N': '正常',
            'S': '自費', 
            'M': '未遇',
            'Z': '預定',
            'C': '取消',
            'R': '休息'
        };
        
        return statusMap[statusCode] || statusCode || '';
    }

    // 顯示錯誤訊息
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: '錯誤',
            text: message
        });
    }
</script>

<%- include('../partials/footer') %>
