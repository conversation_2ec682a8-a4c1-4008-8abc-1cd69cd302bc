<%- include('../partials/header') %>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
<style>
    :root {
        --primary-color: #007bff;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --hover-color: #f5f5f5;
    }

    .page-container {
        padding: 2rem;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    /* .page-header {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        border-left: 4px solid var(--primary-color);
    }

    .page-title {
        color: var(--dark-color);
        font-size: 2rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    } */

     .page-header {
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-align: center;
        color: #333;
    }

    .page-title i {
        color: var(--primary-color);
    }

    .search-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .text-red {
        color: red;
        font-weight: bold;
    }

    .card-header {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
        padding: 1.5rem 2rem;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-body {
        padding: 2rem;
    }

    .search-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-control, .form-select {
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background-color: white;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .input-group {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .input-group .form-control {
        flex: 1;
    }

    .btn-group {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .btn-custom {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        min-width: 120px;
        justify-content: center;
    }

    .btn-search {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
    }

    .btn-search:hover {
        background: linear-gradient(135deg, #0056b3, #004085);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-clear {
        background: linear-gradient(135deg, var(--secondary-color), #5a6268);
        color: white;
    }

    .btn-clear:hover {
        background: linear-gradient(135deg, #5a6268, #495057);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    .btn-add {
        background: linear-gradient(135deg, var(--success-color), #218838);
        color: white;
    }

    .btn-add:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .results-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .results-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 1.5rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .results-title {
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .results-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
    }

    .table-container {
        overflow-x: auto;
        max-height: 600px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        font-size: 0.9rem;
    }

    .table th {
        background: #f8f9fa;
        color: var(--dark-color);
        font-weight: 600;
        padding: 1rem 0.75rem;
        text-align: center;
        border-bottom: 2px solid var(--border-color);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table td {
        padding: 0.75rem;
        text-align: center;
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: var(--hover-color);
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .btn-view {
        background: var(--info-color);
        color: white;
    }

    .btn-view:hover {
        background: #138496;
        transform: translateY(-1px);
    }

    .btn-edit {
        background: var(--warning-color);
        color: var(--dark-color);
    }

    .btn-edit:hover {
        background: #e0a800;
        transform: translateY(-1px);
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    .pagination-container {
        padding: 1.5rem 2rem;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .pagination-info {
        color: var(--secondary-color);
        font-size: 0.9rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .page-btn {
        padding: 0.5rem 0.75rem;
        border: 1px solid var(--border-color);
        background: white;
        color: var(--dark-color);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .page-btn:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .page-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .page-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        white-space: nowrap;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-reviewing {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }

    .status-approved {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    /* 服務摘要彈出視窗樣式 */
    .MyTable {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
    }

    .MyTable .label-cell {
        background-color: #f8f9fa;
        padding: 10px;
        border: 1px solid #dee2e6;
        font-weight: bold;
        width: 120px;
        vertical-align: top;
    }

    .MyTable .input-OneCell {
        padding: 10px;
        border: 1px solid #dee2e6;
        vertical-align: top;
    }

    .AreaHeight200 {
        width: 100%;
        min-height: 200px;
        resize: vertical;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
        font-family: inherit;
    }

    /* 服務摘要彈出視窗樣式 */
    .MyTable {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
    }

    .MyTable .label-cell {
        background-color: #f8f9fa;
        padding: 10px;
        border: 1px solid #dee2e6;
        font-weight: bold;
        width: 120px;
        vertical-align: top;
    }

    .MyTable .input-OneCell {
        padding: 10px;
        border: 1px solid #dee2e6;
        vertical-align: top;
    }

    .AreaHeight200 {
        width: 100%;
        min-height: 200px;
        resize: vertical;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
        font-family: inherit;
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--secondary-color);
        font-style: italic;
    }

    @media (max-width: 768px) {
        .page-container {
            padding: 1rem;
        }

        .search-form {
            grid-template-columns: 1fr;
        }

        .btn-group {
            flex-direction: column;
        }

        .pagination-container {
            flex-direction: column;
            text-align: center;
        }

        .table-container {
            font-size: 0.8rem;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.25rem;
        }
    }
</style>

<div class="page-container">
    <!-- 頁面標題 -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-calendar-day"></i>
            每日服務記錄
        </h1>
    </div>

    <!-- 搜尋表單 -->
    <div class="search-card">
        <div class="card-header">
            <i class="fas fa-search"></i>
            搜尋條件
        </div>
        <div class="card-body">
            <form id="searchForm">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">所屬單位</label>
                        <select class="form-select" id="organization_select" style="width: 100%;"></select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">使用者</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="user_id" name="user_id" placeholder="使用者ID">
                            <input type="text" class="form-control" id="user_name" name="user_name" placeholder="姓名" readonly style="background-color: #CCCCCC;">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">服務年月</label>
                        <input type="month" class="form-control" id="service_year_month" name="service_year_month">
                    </div>

                    <div class="form-group">
                        <label class="form-label">主責督導(B2)/直屬主管(B1)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="duty_member_code" name="duty_member_code" placeholder="代碼">
                            <input type="text" class="form-control" id="duty_member_name" name="duty_member_name" placeholder="姓名" readonly style="background-color: #CCCCCC;">
                        </div>
                    </div> 

                    <div class="form-group">
                        <label class="form-label">服務日期</label>
                        <div class="input-group">
                            <input type="date" class="form-control" id="service_date_start" name="service_date_start">
                            <span style="align-self: center; margin: 0 0.5rem;">至</span>
                            <input type="date" class="form-control" id="service_date_end" name="service_date_end">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">服務人員/請假人員</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="srv_member_code" name="srv_member_code" placeholder="代碼">
                            <input type="text" class="form-control" id="srv_member_name" name="srv_member_name" placeholder="姓名" readonly style="background-color: #CCCCCC;">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">狀態</label>
                        <select class="form-select" id="service_status" name="service_status">
                            <option value="ALL">(全部)</option>
                            <option value="N">正常</option>
                            <option value="S">自費</option>
                            <option value="M">未遇</option>
                            <option value="Z">預定</option>
                            <option value="C">取消</option>
                            <option value="R">休息</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">服務類別</label>
                        <select class="form-select" id="service_type" name="service_type">
                            <option value="ALL">(全部)</option>
                            <!-- 動態載入服務類別選項 -->
                        </select>
                    </div>

                    
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn-custom btn-search">
                        <i class="fas fa-search"></i>
                        搜尋
                    </button>
                    <button type="button" class="btn-custom btn-clear" onclick="clearForm()">
                        <i class="fas fa-eraser"></i>
                        清除
                    </button>
                    <a href="/everyDailyServiceRecords/create" class="btn-custom btn-add el-function-edit">
                        <i class="fas fa-plus"></i>
                        新增資料
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 搜尋結果 -->
    <div class="results-card">
        <div class="results-header">
            <div class="results-title">
                <i class="fas fa-list"></i>
                搜尋結果
            </div>
            <div class="results-count" id="recordCount">
                共 0 筆記錄
            </div>
        </div>

        <div class="table-container">
            <table class="table" id="recordsTable">
                <thead>
                    <tr>
                        <th style="width: 80px;">編輯/刪除/複製</th>
                        <th style="width: 80px;">狀態</th>
                        <th style="width: 60px;">補卡</th>
                        <th style="width: 80px;">案號</th>
                        <th style="width: 100px;">姓名</th>
                        <th style="width: 80px;">類別</th>
                        <th style="width: 100px;">服務日期</th>
                        <th style="width: 100px;">服務人員</th>
                        <th style="width: 120px;">服務時段</th>
                        <th style="width: 80px;">服務時數(小時)</th>
                        <th style="width: 80px;">服務時數(分鐘)</th>
                        <th style="width: 80px;">轉場交通(分鐘)</th>
                        <th style="width: 80px;">累計排班(小時)</th>
                        <th style="width: 60px;">是否不轉場</th>
                        <th style="width: 150px;">服務項目/備註</th>
                        <th style="width: 150px;">服務順序</th>
                        <th style="width: 80px;">服務金額</th>
                        <th style="width: 80px;">歸屬年月</th>
                        <th style="width: 80px;">服務員體溫</th>
                        <th style="width: 80px;">個案體溫</th>
                        <th style="width: 60px;">血壓(高)</th>
                        <th style="width: 60px;">血壓(低)</th>
                        <th style="width: 60px;">脈搏</th>
                        <th style="width: 60px;">呼吸</th>
                        <th style="width: 80px;">通訊區域</th>
                        <th style="width: 80px;">日間18點前</th>
                        <th style="width: 80px;">夜間18點後</th>
                        <th style="width: 120px;">建檔</th>
                        <th style="width: 120px;">修改</th>
                        <th style="width: 100px;">審核日</th>
                    </tr>
                </thead>
                <tbody id="recordsTableBody">
                    <tr>
                        <td colspan="31" class="no-data">
                            <i class="fas fa-search"></i><br>
                            請輸入搜尋條件後點擊「搜尋」按鈕
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="pagination-container">
            <div class="pagination-info" id="paginationInfo">
                顯示第 0 - 0 筆，共 0 筆記錄
            </div>
            <div class="pagination" id="pagination">
                <!-- 分頁按鈕將由JavaScript動態生成 -->
            </div>
        </div>
    </div>
</div>


<%- include('../partials/footer') %>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!--<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>-->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="module">
    import userLookupModule from '/services/userLookupModule.js';
    window.userModuleInstance = userLookupModule();
    window.dutyModuleInstance = userLookupModule();
    window.srvModuleInstance = userLookupModule();
</script>
<script>
    let currentPage = 1;
    let pageSize = 50;
    let totalRecords = 0;

    $(document).ready(function() {
        getOrganizations();
        loadServiceTypes();
        searchRecords();
        // 設定預設日期範圍（最近一個月）
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

        $('#service_date_end').val(today.toISOString().split('T')[0]);
        $('#service_date_start').val(oneMonthAgo.toISOString().split('T')[0]);
        $('#service_year_month').val(today.toISOString().split('T')[0].substring(0, 7));

        userModuleInstance.init('user_id', 'user_name', 'client');
        userModuleInstance.listen();
        dutyModuleInstance.init('duty_member_code', 'duty_member_name', 'employee');
        dutyModuleInstance.listen();
        srvModuleInstance.init('srv_member_code', 'srv_member_name', 'employee');
        srvModuleInstance.listen();

        showFunctionElements("D-1-EDIT");
    });

    // 表單提交事件
    $('#searchForm').submit(function(e) {
        e.preventDefault();
        currentPage = 1;
        searchRecords();
    });

    function getOrganizations() {
        $.ajax({
            type: 'POST',
            url: '/api/get_organizations',
            contentType: 'application/json',
            data: JSON.stringify({ 'withManager': false }),
            async: false,
            success: function (data) {
                let content = "";
                let organizations = data.organizations;
                organizations.forEach(function (organization) {
                    content += `<option value='${organization.id}'>${organization.name}</option>`;
                });
                $('#organization_select').html(content);
                $("#organization_select").select2();
                $("#organization_select").val('2').trigger("change");
            },
            error: function (jqXHR) {
                let errorTitle = `[${jqXHR.status}] - `;
                if (jqXHR.readyState != 4) { errorTitle += '連線發生錯誤'; }
                else { errorTitle += jqXHR.responseJSON ? jqXHR.responseJSON.message : '錯誤' ; }
                
                Swal.fire({
                    title: errorTitle,
                    confirmButtonText: '知道了',
                    confirmButtonColor: 'gray',
                    allowOutsideClick: false
                }).then(() => { location.reload(); });
            }
        });
    }

    // 載入服務類別
    function loadServiceTypes() {
        $.ajax({
            url: '/everyDailyServiceRecords/api/system-codes/SRVTYPE',
            method: 'GET',
            success: function(response) {
                if (response.state === 'success') {
                    const serviceTypeSelect = $('#service_type');
                    // 清空現有選項（保留預設選項）
                    serviceTypeSelect.find('option:not(:first)').remove();

                    // 添加從資料庫載入的選項
                    response.data.forEach(function(item) {
                        serviceTypeSelect.append(
                            $('<option></option>')
                                .attr('value', item.code_id)
                                .text(item.code_name)
                        );
                    });
                } else {
                    console.error('載入服務類別失敗:', response.msg);
                }
            },
            error: function(xhr, status, error) {
                console.error('載入服務類別錯誤:', error);
            }
        });
    }

    // 搜尋記錄
    function searchRecords() {
        const formData = {
            id_number: $('#id_number').val(),
            organization_id: $('#organization_select').val(),
            service_date_start: $('#service_date_start').val(),
            service_date_end: $('#service_date_end').val(),
            duty_member_code: $('#duty_member_code').val(),
            service_type: $('#service_type').val(),
            srv_member_code: $('#srv_member_code').val(),
            service_item: $('#service_item').val(),
            service_year_month: $('#service_year_month').val(),
            service_status: $('#service_status').val(),
            upload_setting: $('#upload_setting').val(),
            keyword: $('#keyword').val(),
            page: currentPage,
            pageSize: pageSize
        };

        $.ajax({
            url: '/everyDailyServiceRecords/records',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                console.log('搜尋結果:', response);
                if (response.state === 'success') {
                    displayRecords(response.data);
                    updatePagination(response.total, response.page, response.pageSize, response.totalPages);
                } else {
                    showError(response.msg || '搜尋失敗');
                }
                showFunctionElements("D-1-EDIT");
            },
            error: function(xhr, status, error) {
                console.error('搜尋錯誤:', error);
                showError('搜尋時發生錯誤: ' + error);
            }
        });
    }

    // 顯示記錄
    function displayRecords(records) {
        const tbody = $('#recordsTableBody');
        tbody.empty();

        if (records.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="31" class="no-data">
                        <i class="fas fa-inbox"></i><br>
                        查無符合條件的記錄
                    </td>
                </tr>
            `);
            return;
        }

        records.forEach(record => {
            const targets = record.targets ? JSON.parse(record.targets).join(', ') : '';
            const serviceItems = record.service_items ? JSON.parse(record.service_items).join(', ') : '';

            const row = `
                <tr>
                    <td>
                        <div class="action-buttons">
                            <a href="/everyDailyServiceRecords/edit/${record.id}" class="btn-sm btn-edit el-function-edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button class="btn-sm btn-delete el-function-edit" onclick="deleteRecord(${record.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                    <td>
                        ${getServiceStatusText(record.service_status)}
                        <br />
                        <span class="${record.service_desc ? 'text-primary' : 'text-dark'}" style="cursor: pointer; text-decoration: underline;" onclick="showServiceSummary('${record.id}', '${record.record_number}', '${record.full_name}', '${formatDate(record.service_date)} ${record.time1 ? record.time1.substring(0, 5) : ''}~${record.time2 ? record.time2.substring(0, 5) : ''}', \`${record.service_desc ? record.service_desc.replace(/`/g, '\\`').replace(/\n/g, '\\n') : ''}\`, '${record.service_desc_updated_at || ''}')">${record.service_desc ? '[有摘要]' : '[無摘要]'}</span>
                    </td>
                    <td>
                        <a href="/everyDailyServiceRecords/makeup-clock/${record.id}" class="btn-sm btn-warning el-function-edit">
                            <i class="fas fa-clock"></i>
                        </a>
                    </td>
                     <td>
                        ${record.record_number || ''}
                    </td>
                    <td>${record.puid || ''}
                        ${record.full_name || ''}</td>
                    <td>${record.service_type || ''} ${getServiceTypeText(record.service_type) || ''}</td>
                    <td>${formatDate(record.service_date)}</td>
                    <td>${record.uid || ''}
                        ${record.duty_member_name || ''}</td>
                    <td>${record.time1 ? record.time1.substring(0, 5) : ''}~${record.time2 ? record.time2.substring(0, 5) : ''}
                        ${record.clock_in_time != null?
                        `<br /><span class="text-red">${record.clock_in_time ? record.clock_in_time.split(' ')[1].substring(0, 5) || record.clock_in_time : ''}~${record.clock_out_time ? record.clock_out_time.split(' ')[1].substring(0, 5) || record.clock_out_time : ''}</span>` : ''}
                    </td>
                    <td>${record.service_hour || ''}
                        ${record.actual_service_minutes != 0?
                        `<br /><span class="text-red">${(record.actual_service_minutes/60).toFixed(2) || ''}</span>` : ''}</td>
                    <td>${record.service_min || ''}
                        ${record.actual_service_minutes != null?
                        `<br /><span class="text-red">${record.actual_service_minutes || ''}</span>` : ''}
                    </td>
                    <td>${record.traffic_min || ''}</td>
                    <td>-</td>
                    <td>${record.traffic_times === 0 ? '不轉場' : '轉場'}</td>
                    <td>${record.srv_item || ''} ${record.remark ? '/ ' + record.remark : ''}</td>
                    <td>-</td>
                    <td>${record.service_total_price || 0}</td>
                    <td>${record.service_ym || ''}</td>
                    <td>${record.member_temperature || '-'}</td>
                    <td>${record.temperature || '-'}</td>
                    <td>${record.sbp || '-'}</td>
                    <td>${record.dbp || '-'}</td>
                    <td>${record.pulse || '-'}</td>
                    <td>${record.breathe || '-'}</td>
                    <td>${record.address.substring(0, 6) || '-'}</td>
                    <td>${calculateDaytimeHours(record.time1, record.time2)}</td>
                    <td>${calculateNighttimeHours(record.time1, record.time2)}</td>
                    <td>${formatDateTime(record.created_at)}</td>
                    <td>${formatDateTime(record.updated_at)}</td>
                    <td>${formatDateTime(record.sign_date)}</td>
                </tr>
            `;
            tbody.append(row);
        });
    }

    // 更新分頁
    function updatePagination(total, page, size, totalPages) {
        totalRecords = total;
        $('#recordCount').text(`共 ${total} 筆記錄`);

        const start = total > 0 ? (page - 1) * size + 1 : 0;
        const end = Math.min(page * size, total);
        $('#paginationInfo').text(`顯示第 ${start} - ${end} 筆，共 ${total} 筆記錄`);

        const pagination = $('#pagination');
        pagination.empty();

        if (totalPages <= 1) return;

        // 上一頁
        if (page > 1) {
            pagination.append(`<button class="page-btn" onclick="changePage(${page - 1})">上一頁</button>`);
        }

        // 頁碼
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        if (startPage > 1) {
            pagination.append(`<button class="page-btn" onclick="changePage(1)">1</button>`);
            if (startPage > 2) {
                pagination.append(`<span>...</span>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            pagination.append(`<button class="page-btn ${activeClass}" onclick="changePage(${i})">${i}</button>`);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagination.append(`<span>...</span>`);
            }
            pagination.append(`<button class="page-btn" onclick="changePage(${totalPages})">${totalPages}</button>`);
        }

        // 下一頁
        if (page < totalPages) {
            pagination.append(`<button class="page-btn" onclick="changePage(${page + 1})">下一頁</button>`);
        }
    }

    // 換頁
    function changePage(page) {
        currentPage = page;
        searchRecords();
    }

    // 清除表單
    function clearForm() {
        $('#searchForm')[0].reset();

        // 重新設定預設日期
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

        $('#service_date_end').val(today.toISOString().split('T')[0]);
        $('#service_date_start').val(oneMonthAgo.toISOString().split('T')[0]);
        $('#service_year_month').val(today.toISOString().split('T')[0].substring(0, 7));
        // 清除結果
        $('#recordsTableBody').html(`
            <tr>
                <td colspan="14" class="no-data">
                    <i class="fas fa-search"></i><br>
                    請輸入搜尋條件後點擊「搜尋」按鈕
                </td>
            </tr>
        `);
        $('#recordCount').text('共 0 筆記錄');
        $('#paginationInfo').text('顯示第 0 - 0 筆，共 0 筆記錄');
        $('#pagination').empty();
    }

    // 刪除記錄
    function deleteRecord(id) {
        Swal.fire({
            title: '確認刪除',
            text: '您確定要刪除這筆記錄嗎？此操作無法復原。',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '確定刪除',
            cancelButtonText: '取消'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/everyDailyServiceRecords/delete-record/${id}`,
                    method: 'POST',
                    success: function(response) {
                        if (response.state === 'success') {
                            Swal.fire({
                                icon: 'success',
                                title: '刪除成功',
                                text: '記錄已成功刪除',
                                showConfirmButton: false,
                                timer: 2000
                            });
                            searchRecords(); // 重新載入資料
                        } else {
                            showError(response.msg || '刪除失敗');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('刪除錯誤:', error);
                        showError('刪除時發生錯誤: ' + error);
                    }
                });
            }
        });
    }

    // 格式化日期
    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW');
    }

    // 格式化日期時間
    function formatDateTime(dateTimeString) {
        if (!dateTimeString) return '';
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-TW');
    }

    // 計算日間18點前的服務時數
    function calculateDaytimeHours(time1, time2) {
        if (!time1 || !time2) return '0.00';

        // 將時間字串轉換為分鐘數
        const startMinutes = timeToMinutes(time1);
        const endMinutes = timeToMinutes(time2);
        const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘

        let daytimeMinutes = 0;

        if (endMinutes <= cutoffMinutes) {
            // 完全在18點前
            daytimeMinutes = endMinutes - startMinutes;
        } else if (startMinutes < cutoffMinutes) {
            // 跨越18點
            daytimeMinutes = cutoffMinutes - startMinutes;
        }
        // 如果完全在18點後，daytimeMinutes 保持為 0

        return (daytimeMinutes / 60).toFixed(2);
    }

    // 計算夜間18點後的服務時數
    function calculateNighttimeHours(time1, time2) {
        if (!time1 || !time2) return '0.00';

        // 將時間字串轉換為分鐘數
        const startMinutes = timeToMinutes(time1);
        const endMinutes = timeToMinutes(time2);
        const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘

        let nighttimeMinutes = 0;

        if (startMinutes >= cutoffMinutes) {
            // 完全在18點後
            nighttimeMinutes = endMinutes - startMinutes;
        } else if (endMinutes > cutoffMinutes) {
            // 跨越18點
            nighttimeMinutes = endMinutes - cutoffMinutes;
        }
        // 如果完全在18點前，nighttimeMinutes 保持為 0

        return (nighttimeMinutes / 60).toFixed(2);
    }

    // 將時間字串 (HH:MM:SS 或 HH:MM) 轉換為分鐘數
    function timeToMinutes(timeString) {
        if (!timeString) return 0;

        const parts = timeString.split(':');
        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;

        return hours * 60 + minutes;
    }

    // 轉換服務狀態代號為中文文字
    function getServiceStatusText(statusCode) {
        const statusMap = {
            'N': '正常',
            'S': '自費',
            'M': '未遇',
            'Z': '預定',
            'C': '取消',
            'R': '休息'
        };

        return statusMap[statusCode] || statusCode || '';
    }

    // 轉換服務類型代號為中文文字
    function getServiceTypeText(typeCode) {
        const typeMap = {
            '5': '照服',
            '6': '日照',
            '4': '短照',
            '7': '喘息',
            'S': '自費方案'
        };

        return typeMap[typeCode] || typeCode || '';
    }

    // 顯示錯誤訊息
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: '錯誤',
            text: message
        });
    }

    // 顯示服務摘要彈出視窗
    function showServiceSummary(recordId, caseNo, userName, serviceTime, serviceDesc, lastUpdated) {
        $('#ID_Number').text(caseNo);
        $('#Name_CH').text(userName);
        $('#Service_DateX').text(serviceTime);
        $('#Service_Desc').val(serviceDesc);
        $('#lastUpdatedTime').text(lastUpdated ? `最後異動時間：${lastUpdated}` : '');
        $('#currentRecordId').val(recordId);
        $('#serviceSummaryModal').modal('show');
    }

    // 儲存服務摘要
    function saveServiceSummary() {
        const recordId = $('#currentRecordId').val();
        const serviceDesc = $('#Service_Desc').val();

        $.ajax({
            type: 'POST',
            url: '/everyDailyServiceRecords/update-service-desc',
            contentType: 'application/json',
            data: JSON.stringify({
                recordId: recordId,
                serviceDesc: serviceDesc
            }),
            success: function(response) {
                if (response.state === 'success') {
                    $('#serviceSummaryModal').modal('hide');
                    Swal.fire({
                        icon: 'success',
                        title: '成功',
                        text: '服務摘要更新成功'
                    }).then(() => {
                        
                        searchRecords(); // 重新載入資料
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '錯誤',
                        text: response.msg || '更新失敗'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('更新服務摘要錯誤:', error);
                Swal.fire({
                    icon: 'error',
                    title: '錯誤',
                    text: '更新服務摘要時發生錯誤'
                });
            }
        });
    }
</script>

<!-- 服務摘要彈出視窗 -->
<div class="modal fade" id="serviceSummaryModal" tabindex="-1" aria-labelledby="serviceSummaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="serviceSummaryModalLabel">服務摘要</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="MyTable">
                    <tbody>
                        <tr>
                            <td class="label-cell">
                                <span id="Label20">使用者：</span>
                            </td>
                            <td class="input-OneCell">
                                <span id="ID_Number"></span>
                                <span id="Name_CH"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                <span id="Label21">服務時間：</span>
                            </td>
                            <td class="input-OneCell">
                                <span id="Service_DateX"></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="label-cell">
                                <span id="Label12">服務摘要：</span>
                            </td>
                            <td class="input-OneCell">
                                <textarea name="Service_Desc" rows="2" cols="20" id="Service_Desc" class="AreaHeight200"></textarea>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div id="lastUpdatedTime" class="text-muted mt-2"></div>
                <input type="hidden" id="currentRecordId" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="saveServiceSummary()">儲存</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
            </div>
        </div>
    </div>
</div>


