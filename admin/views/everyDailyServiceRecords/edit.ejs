<%- include('../partials/header') %>

<style>
    .edit-container {
        padding: 2rem;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .edit-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header i {
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #343a40;
        margin-bottom: 0.5rem;
        display: block;
    }

    .required {
        color: #dc3545;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1rem;
    }

    .time-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .time-group input {
        flex: 1;
    }

    .btn-group {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-save {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .btn-save:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        transform: translateY(-2px);
    }

    .btn-cancel {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
    }

    .btn-cancel:hover {
        background: linear-gradient(135deg, #5a6268, #495057);
        transform: translateY(-2px);
    }

    .readonly-info {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #17a2b8;
        margin-bottom: 1.5rem;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .btn-group {
            flex-direction: column;
        }
    }
</style>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">編輯服務記錄</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首頁</a></li>
                        <li class="breadcrumb-item"><a href="/everyDailyServiceRecords">日常服務記錄</a></li>
                        <li class="breadcrumb-item active">編輯記錄</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="edit-container">
                <form id="editForm">
                    <input type="hidden" id="record_id" value="<%= recordId %>">
                    
                    <!-- 基本資訊 -->
                    <div class="readonly-info">
                        <strong>記錄ID:</strong> <%= recordId %> | 
                        <strong>案號:</strong> <span id="display_puid"></span> | 
                        <strong>姓名:</strong> <span id="display_name"></span>
                    </div>

                    <!-- 服務記錄資訊 -->
                    <div class="edit-card">
                        <div class="card-header">
                            <i class="fas fa-edit"></i>
                            服務記錄資訊
                        </div>
                        <div class="card-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">狀態 <span class="required">*</span></label>
                                    <select class="form-select input-function-edit" id="service_status" name="service_status" required>
                                        <option value="">請選擇狀態</option>
                                        <option value="N">正常</option>
                                        <option value="S">自費</option>
                                        <option value="M">未遇</option>
                                        <option value="Z">預定</option>
                                        <option value="C">取消</option>
                                        <option value="R">休息</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">類別 <span class="required">*</span></label>
                                    <select class="form-select input-function-edit" id="service_type" name="service_type" required>
                                        <option value="">請選擇類別</option>
                                        <option value="5">照服</option>
                                        <option value="6">日照</option>
                                        <option value="4">短照</option>
                                        <option value="7">喘息</option>
                                        <option value="S">自費方案</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">服務日期 <span class="required">*</span></label>
                                    <input type="date" class="form-control input-function-edit" id="service_date" name="service_date" required>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">服務人員</label>
                                    <input type="text" class="form-control" id="srv_member_code" name="srv_member_code" 
                                           placeholder="服務人員代碼" maxlength="20" style="width: 120px; display: inline-block;" required>
                                    <input type="text" class="form-control readonly-input" id="srv_member_name" name="srv_member_name" 
                                           readonly style="width: 120px; display: inline-block; margin-left: 10px;">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">服務時段 <span class="required">*</span></label>
                                    <div class="time-group">
                                        <input type="time" class="form-control input-function-edit" id="time1" name="time1" required>
                                        <span>至</span>
                                        <input type="time" class="form-control input-function-edit" id="time2" name="time2" required>
                                    </div>
                                    <div class="time-inputs time-group">
                                        <span>共計</span>
                                        <input type="text" class="form-control" id="service_hour" name="service_hour" 
                                            readonly value="0">
                                        <span>小時，或</span>
                                        <input type="text" class="form-control" id="service_min" name="service_min" 
                                            readonly value="0">
                                        <span>分鐘</span>
                                    </div>
                                </div>
                                

                                <div class="form-group">
                                    <label class="form-label">是否轉場</label>
                                    <select class="form-select input-function-edit" id="traffic_times" name="traffic_times">
                                        <option value="1">轉場</option>
                                        <option value="0">不轉場</option>
                                    </select>
                                    <span>轉場交通(分鐘)</span>
                                    <input type="text" class="form-control" id="traffic_min" name="traffic_min" 
                                            value="0">
                                    
                                    
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">服務項目</label>
                                    <textarea class="form-control input-function-edit" id="srv_item" name="srv_item" rows="3" placeholder="請輸入服務項目"></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">備註</label>
                                    <textarea class="form-control input-function-edit" id="remark" name="remark" rows="3" placeholder="請輸入備註"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生理量測資訊 -->
                    <div class="edit-card">
                        <div class="card-header">
                            <i class="fas fa-heartbeat"></i>
                            生理量測資訊
                        </div>
                        <div class="card-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">服務員體溫</label>
                                    <input type="number" class="form-control input-function-edit" id="member_temperature" name="member_temperature" 
                                           step="0.1" min="33" max="42" placeholder="例如: 36.5">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">個案體溫</label>
                                    <input type="number" class="form-control input-function-edit" id="temperature" name="temperature" 
                                           step="0.1" min="33" max="42" placeholder="例如: 36.5">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">血壓（高）</label>
                                    <input type="number" class="form-control input-function-edit" id="sbp" name="sbp" 
                                           min="80" max="200" placeholder="收縮壓 例如: 120">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">血壓（低）</label>
                                    <input type="number" class="form-control input-function-edit" id="dbp" name="dbp" 
                                           min="40" max="120" placeholder="舒張壓 例如: 80">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">脈搏</label>
                                    <input type="number" class="form-control input-function-edit" id="pulse" name="pulse" 
                                           min="40" max="200" placeholder="次/分 例如: 72">
                                </div>

                                <div class="form-group">
                                    <label class="form-label">呼吸</label>
                                    <input type="number" class="form-control input-function-edit" id="breathe" name="breathe" 
                                           min="10" max="60" placeholder="次/分 例如: 18">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按鈕群組 -->
                    <div class="btn-group">
                        <button type="submit" class="btn btn-save el-function-edit">
                            <i class="fas fa-save"></i> 儲存
                        </button>
                        <a href="/everyDailyServiceRecords" class="btn btn-cancel">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<%- include('../partials/footer') %>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="module">
    import userLookupModule from '/services/userLookupModule.js';
    window.srvModuleInstance = userLookupModule();
</script>
<script>
    $(document).ready(function() {
        showFunctionElements("D-1-EDIT");
        // 載入記錄資料
        loadRecordData();

        srvModuleInstance.init('srv_member_code', 'srv_member_name', 'employee');
        srvModuleInstance.listen();

        // 時間變更時自動計算服務時數
        $('#time1, #time2').on('change', function() {
            calculateServiceHours();
        });

        // 表單提交
        $('#editForm').on('submit', function(e) {
            e.preventDefault();
            saveRecord();
        });
    });

    // 計算服務時數
    function calculateServiceHours() {
        const time1 = $('#time1').val();
        const time2 = $('#time2').val();
        
        if (time1 && time2) {
            const start = new Date('2000-01-01 ' + time1);
            const end = new Date('2000-01-01 ' + time2);
            
            if (end > start) {
                const diffMs = end - start;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                const totalMinutes = diffHours * 60 + diffMinutes;
                
                $('#service_hour').val(diffHours);
                $('#service_min').val(totalMinutes);
            }
        }
    }

    // 載入記錄資料
    function loadRecordData() {
        const recordId = $('#record_id').val();
        
        $.ajax({
            url: `/everyDailyServiceRecords/data/${recordId}`,
            method: 'GET',
            success: function(response) {
                if (response.state === 'success') {
                    const data = response.data;
                    
                    // 填入基本資訊顯示
                    $('#display_puid').text(data.puid || '');
                    $('#display_name').text(data.full_name || '');
                    
                    // 填入服務記錄資訊
                    $('#service_status').val(data.service_status || '');
                    $('#service_type').val(data.service_type || '');
                    $('#service_date').val(data.service_date ? data.service_date.split('T')[0] : '');
                    $('#srv_member_code').val(data.employee_number || '');
                    $('#srv_member_name').val(data.duty_member_name || '');
                    $('#time1').val(data.time1 || '');
                    $('#time2').val(data.time2 || '');
                    $('#traffic_times').val(data.traffic_times || '1');
                    $('#traffic_min').val(data.traffic_min || '0');
                    $('#srv_item').val(data.srv_item || '');
                    $('#remark').val(data.remark || '');
                    
                    // 填入生理量測資訊
                    $('#member_temperature').val(data.member_temperature || '');
                    $('#temperature').val(data.temperature || '');
                    $('#sbp').val(data.sbp || '');
                    $('#dbp').val(data.dbp || '');
                    $('#pulse').val(data.pulse || '');
                    $('#breathe').val(data.breathe || '');
                    calculateServiceHours();
                } else {
                    showError('載入記錄失敗: ' + response.msg);
                }
            },
            error: function(xhr, status, error) {
                showError('載入記錄時發生錯誤: ' + error);
            }
        });
    }

    // 儲存記錄
    function saveRecord() {
        // 驗證必填欄位
        if (!$('#service_status').val()) {
            showError('請選擇狀態');
            return;
        }
        if (!$('#service_type').val()) {
            showError('請選擇類別');
            return;
        }
        if (!$('#service_date').val()) {
            showError('請選擇服務日期');
            return;
        }
        if (!$('#time1').val() || !$('#time2').val()) {
            showError('請填寫完整的服務時段');
            return;
        }

        // 收集表單資料
        const formData = {
            id: $('#record_id').val(),
            service_status: $('#service_status').val(),
            service_type: $('#service_type').val(),
            service_date: $('#service_date').val(),
            srv_member_code: $('#srv_member_code').val(),
            time1: $('#time1').val(),
            time2: $('#time2').val(),
            service_hour: $('#service_hour').val(),
            service_min: $('#service_min').val(),
            traffic_times: $('#traffic_times').val(),
            traffic_min: $('#traffic_min').val(),
            srv_item: $('#srv_item').val(),
            remark: $('#remark').val(),
            member_temperature: $('#member_temperature').val(),
            temperature: $('#temperature').val(),
            sbp: $('#sbp').val(),
            dbp: $('#dbp').val(),
            pulse: $('#pulse').val(),
            breathe: $('#breathe').val()
        };

        // 顯示載入中
        Swal.fire({
            title: '儲存中...',
            text: '正在更新記錄',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 發送更新請求
        $.ajax({
            url: '/everyDailyServiceRecords/update',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.state === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: '儲存成功',
                        text: response.msg || '記錄已成功更新',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        window.location.href = '/everyDailyServiceRecords';
                    });
                } else {
                    showError(response.msg || '儲存失敗');
                }
            },
            error: function(xhr, status, error) {
                showError('儲存時發生錯誤: ' + error);
            }
        });
    }

    // 顯示錯誤訊息
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: '錯誤',
            text: message
        });
    }
</script>


