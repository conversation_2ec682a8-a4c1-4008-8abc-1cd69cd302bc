<%- include('../partials/header') %>

<style>
    .page-container {
        padding: 2rem;
        background-color: #f8f9fa;
        min-height: 100vh;
    }

    .form-table {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .form-table table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .form-table td {
        padding: 12px 15px;
        border: 1px solid #dee2e6;
        vertical-align: middle;
    }

    .form-table .title-cell {
        background-color: #f8f9fa;
        font-weight: bold;
        width: 150px;
        text-align: left;
    }

    .required {
        color: #FF0000;
        font-size: 16px;
        margin-right: 5px;
    }

    .form-control, .form-select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .readonly-input {
        background-color: #CCCCCC !important;
        color: #000000;
    }

    .time-inputs {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .time-inputs input[type="date"] {
        width: 140px;
    }

    .time-inputs input[type="time"] {
        width: 130px;
    }

    .time-inputs input[type="text"] {
        width: 60px;
        text-align: center;
    }

    .radio-group {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }

    .radio-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .btn-group {
        text-align: center;
        margin-top: 30px;
        padding: 20px;
    }

    .btn {
        padding: 10px 30px;
        margin: 0 10px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .btn-save {
        background-color: #28a745;
        color: white;
    }

    .btn-save:hover {
        background-color: #218838;
    }

    .btn-cancel {
        background-color: #6c757d;
        color: white;
    }

    .btn-cancel:hover {
        background-color: #5a6268;
    }

    .service-item-container {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .service-item-container textarea {
        flex: 1;
        min-height: 60px;
        resize: vertical;
    }

    .btn-select {
        padding: 8px 15px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        white-space: nowrap;
    }

    .btn-select:hover {
        background-color: #0056b3;
    }

    @media (max-width: 768px) {
        .time-inputs {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .radio-group {
            flex-direction: column;
            gap: 10px;
        }
        
        .service-item-container {
            flex-direction: column;
        }
    }
</style>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mt-4">
                <div class="col-sm-12 text-center">
                    <h1 class="m-0">新增每日服務記錄</h1>
                </div>
                <!-- <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首頁</a></li>
                        <li class="breadcrumb-item"><a href="/everyDailyServiceRecords">每日服務記錄</a></li>
                        <li class="breadcrumb-item active">新增記錄</li>
                    </ol>
                </div> -->
            </div>
        </div>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="page-container">
                <form id="recordForm">
                    <!-- 基本資訊 -->
                    <div class="form-table">
                        <table>
                            <tr>
                                <td class="title-cell">所屬單位：</td>
                                <td>
                                    <select class="form-select" id="organization_select" style="width: 100%;" readonly></select>
                                </td>
                            </tr>
                            <tr>
                                <td class="title-cell">
                                    <span class="required">*</span>使用者：
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="user_id" name="user_id" 
                                           placeholder="使用者代碼" maxlength="20" style="width: 120px; display: inline-block;" required>
                                    <input type="text" class="form-control readonly-input" id="user_name" name="user_name" 
                                           readonly style="width: 120px; display: inline-block; margin-left: 10px;">
                                </td>

                            </tr>
                            <tr>
                                <td class="title-cell">
                                    <span class="required">*</span>服務人員：
                                </td>
                                <td>
                                    <input type="text" class="form-control" id="srv_member_code" name="srv_member_code" 
                                           placeholder="服務人員代碼" maxlength="20" style="width: 120px; display: inline-block;" required>
                                    <input type="text" class="form-control readonly-input" id="srv_member_name" name="srv_member_name" 
                                           readonly style="width: 120px; display: inline-block; margin-left: 10px;">
                                </td>
                            </tr>
                            <tr>
                                <td class="title-cell">
                                    <span class="required">*</span>服務時段：
                                </td>
                                <td>
                                    <div class="time-inputs">
                                        <input type="date" class="form-control" id="service_date" name="service_date" required>
                                        <span>時間：</span>
                                        <input type="time" class="form-control" id="time1" name="time1" required>
                                        <span>至</span>
                                        <input type="time" class="form-control" id="time2" name="time2" required>
                                    </div>
                                    <div class="time-inputs">
                                        <span>共計</span>
                                        <input type="text" class="form-control" id="service_hour" name="service_hour" 
                                               readonly value="0">
                                        <span>小時，或</span>
                                        <input type="text" class="form-control" id="service_min" name="service_min" 
                                               readonly value="0">
                                        <span>分鐘，交通</span>
                                        <input type="text" class="form-control" id="traffic_min" name="traffic_min" 
                                               value="0">
                                        <span>分鐘</span>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- 服務詳細資訊 -->
                    <div class="form-table">
                        <table>
                            <tr>
                                <td class="title-cell">
                                    <span class="required">*</span>服務類別：
                                </td>
                                <td>
                                    <select class="form-select" id="service_type" name="service_type" style="width: 200px;" required>
                                        <option value="">請選擇</option>
                                        <!-- 動態載入服務類別選項 -->
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td class="title-cell">服務項目：</td>
                                <td>
                                    <div class="service-item-container">
                                        <textarea class="form-control" id="srv_item" name="srv_item" 
                                                  placeholder="請輸入服務項目" style="width: 400px;"></textarea>
                                        <button type="button" class="btn-select" onclick="openServiceItemSelector()">
                                            選擇
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- 狀態和設定 -->
                    <div class="form-table">
                        <table>
                            <tr>
                                <td class="title-cell">狀態：</td>
                                <td>
                                    <div class="radio-group">
                                        <div class="radio-item">
                                            <input type="radio" id="status_N" name="service_status" value="N" checked>
                                            <label for="status_N">正常</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="status_S" name="service_status" value="S">
                                            <label for="status_S">自費</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="status_M" name="service_status" value="M">
                                            <label for="status_M">未遇</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="status_Z" name="service_status" value="Z">
                                            <label for="status_Z">預定</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="status_C" name="service_status" value="C">
                                            <label for="status_C">取消</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="status_R" name="service_status" value="R">
                                            <label for="status_R">休息</label>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="title-cell">是否轉場：</td>
                                <td>
                                    <div class="radio-group">
                                        <div class="radio-item">
                                            <input type="radio" id="traffic_yes" name="traffic_times" value="1" checked>
                                            <label for="traffic_yes">轉場</label>
                                        </div>
                                        <div class="radio-item">
                                            <input type="radio" id="traffic_no" name="traffic_times" value="0">
                                            <label for="traffic_no">不轉場</label>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="title-cell">備註說明：</td>
                                <td>
                                    <textarea class="form-control" id="remark" name="remark" rows="3" 
                                              style="width: 100%;" placeholder="請輸入備註說明"></textarea>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- 按鈕群組 -->
                    <div class="btn-group">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save"></i> 儲存
                        </button>
                        <a href="/everyDailyServiceRecords" class="btn btn-cancel">
                            <i class="fas fa-times"></i> 取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<%- include('../partials/footer') %>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="module">
    import userLookupModule from '/services/userLookupModule.js';
    window.userModuleInstance = userLookupModule();
    window.srvModuleInstance = userLookupModule();
</script>
<script>
    $(document).ready(function() {
        showFunctionElements("D-1-EDIT");
        getOrganizations();
        loadServiceTypes(); // 載入服務類別

        userModuleInstance.init('user_id', 'user_name', 'client');
        userModuleInstance.listen();
        srvModuleInstance.init('srv_member_code', 'srv_member_name', 'employee');
        srvModuleInstance.listen();

        // 設定預設日期為今天
        const today = new Date().toISOString().split('T')[0];
        $('#service_date').val(today);

        // 時間變更時自動計算服務時數
        $('#time1, #time2').on('change', function() {
            calculateServiceHours();
        });

        // 表單提交
        $('#recordForm').submit(function(e) {
            e.preventDefault();
            saveRecord();
        });
    });

    function getOrganizations() {
        $.ajax({
            type: 'POST',
            url: '/api/get_organizations',
            contentType: 'application/json',
            data: JSON.stringify({ 'withManager': false }),
            async: false,
            success: function (data) {
                let content = "";
                let organizations = data.organizations;
                organizations.forEach(function (organization) {
                    content += `<option value='${organization.id}'>${organization.name}</option>`;
                });
                $('#organization_select').html(content);
                //$("#organization_select").select2();
                $("#organization_select").val('2').trigger("change");
            },
            error: function (jqXHR) {
                let errorTitle = `[${jqXHR.status}] - `;
                if (jqXHR.readyState != 4) { errorTitle += '連線發生錯誤'; }
                else { errorTitle += jqXHR.responseJSON ? jqXHR.responseJSON.message : '錯誤' ; }
                
                Swal.fire({
                    title: errorTitle,
                    confirmButtonText: '知道了',
                    confirmButtonColor: 'gray',
                    allowOutsideClick: false
                }).then(() => { location.reload(); });
            }
        });
    }

    // 計算服務時數
    function calculateServiceHours() {
        const time1 = $('#time1').val();
        const time2 = $('#time2').val();
        
        if (time1 && time2) {
            const start = new Date('2000-01-01 ' + time1);
            const end = new Date('2000-01-01 ' + time2);
            
            if (end > start) {
                const diffMs = end - start;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                const totalMinutes = diffHours * 60 + diffMinutes;
                
                $('#service_hour').val(diffHours);
                $('#service_min').val(totalMinutes);
            }
        }
    }

    // 載入服務類別
    function loadServiceTypes() {
        $.ajax({
            url: '/everyDailyServiceRecords/api/system-codes/SRVTYPE',
            method: 'GET',
            success: function(response) {
                if (response.state === 'success') {
                    const serviceTypeSelect = $('#service_type');
                    // 清空現有選項（保留預設選項）
                    serviceTypeSelect.find('option:not(:first)').remove();

                    // 添加從資料庫載入的選項
                    response.data.forEach(function(item) {
                        serviceTypeSelect.append(
                            $('<option></option>')
                                .attr('value', item.code_id)
                                .text(item.code_name)
                        );
                    });
                } else {
                    console.error('載入服務類別失敗:', response.msg);
                }
            },
            error: function(xhr, status, error) {
                console.error('載入服務類別錯誤:', error);
            }
        });
    }

    // 開啟服務項目選擇器
    function openServiceItemSelector() {
        alert('服務項目選擇功能待實作');
    }

    // 儲存記錄
    function saveRecord() {
        // 收集表單資料
        const formData = {
            organization_select: $('#organization_select').val(),
            user_id: $('#user_id').val(),
            user_name: $('#user_name').val(),
            srv_member_code: $('#srv_member_code').val(),
            srv_member_name: $('#srv_member_name').val(),
            service_date: $('#service_date').val(),
            time1: $('#time1').val(),
            time2: $('#time2').val(),
            service_hour: $('#service_hour').val(),
            service_min: $('#service_min').val(),
            traffic_min: $('#traffic_min').val(),
            service_type: $('#service_type').val(),
            srv_item: $('#srv_item').val(),
            service_status: $('input[name="service_status"]:checked').val(),
            traffic_times: $('input[name="traffic_times"]:checked').val(),
            remark: $('#remark').val()
        };

        // 驗證必填欄位
        if (!formData.service_date) {
            showError('請選擇服務日期');
            return;
        }

        if (!formData.user_id) {
            showError('請輸入使用者');
            return;
        }

        if (!formData.srv_member_code) {
            showError('請輸入服務人員代碼');
            return;
        }

        if (!formData.time1 || !formData.time2) {
            showError('請填寫完整的服務時段');
            return;
        }

        if (!formData.service_type) {
            showError('請選擇服務類別');
            return;
        }

        // 顯示載入中
        Swal.fire({
            title: '儲存中...',
            text: '正在建立記錄',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // 發送請求
        $.ajax({
            url: '/everyDailyServiceRecords/save-record',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.state === 'success') {
                    Swal.fire({
                        icon: 'success',
                        title: '儲存成功',
                        text: '每日服務記錄已成功建立',
                        showConfirmButton: false,
                        timer: 2000
                    }).then(() => {
                        window.location.href = '/everyDailyServiceRecords';
                    });
                } else {
                    showError(response.msg || '儲存失敗');
                }
            },
            error: function(xhr, status, error) {
                console.error('儲存錯誤:', error);
                showError('儲存時發生錯誤: ' + error);
            }
        });
    }

    // 顯示錯誤訊息
    function showError(message) {
        Swal.fire({
            icon: 'error',
            title: '錯誤',
            text: message
        });
    }
</script>


