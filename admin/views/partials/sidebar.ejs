<style>
	#slide_ul {
		overflow-x: hidden;
		overflow-y: auto;
	}

	#slide_ul .menu-header, #slide_ul .menu-item {
		display: none;
	}
	
	.layout-wrapper:not(.layout-horizontal) .bg-menu-theme .menu-inner > .menu-item.active:before {
		width: 0 !important;
	}

	#slide_ul .menu-icon {
		font-size: 16px !important;
	}

	.menu-vertical .menu-header {
		padding: .1rem 2rem .1rem 2rem;
	}

	.bg-menu-theme .menu-header:before {
		top: 1rem;
	}

	.menu-vertical .menu-item .menu-link, .menu-vertical .menu-block {
		padding: .3rem 1rem;
		font-size: 14px !important;
	}
</style>
<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
	<div class="app-brand demo">
		<a href="/" class="app-brand-link">
			<span class="app-brand-logo demo">
				<img src="/images/cscclogo.svg" alt="Logo" class="w-100" style="max-width: 200px;" />
			</span>
			<!-- <span class="app-brand-text demo menu-text fw-bolder ms-2">御曲</span> -->
		</a>
		<a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
		<i class="bx bx-chevron-left bx-sm align-middle"></i>
		</a>
	</div>
	<div class="menu-inner-shadow"></div>
	<ul id="slide_ul" class="menu-inner py-1">
		<li class="menu-header small text-uppercase" data-function="N1">
			<span class="menu-header-text">個案管理</span>
		</li>
		<% if (role == 18 || role == 19) { %>
		<li class="menu-item" data-function="N1-1">
			<a id="/all" href="/all/clients_A" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">個案總覽</div>
			</a>
		</li>
		<% } else { %>
		<li class="menu-item" data-function="N1-1">
			<a id="/all" href="/all" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">個案總覽</div>
			</a>
		</li>
		<% } %>
		<li class="menu-item" data-function="N1-2">
			<a id="/addClients" href="/addClients" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">新增個案</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="N2">
			<span class="menu-header-text">行政管理</span>
		</li>
		<li class="menu-item" data-function="N2-1">
			<a id="/careSchedule" href="/careSchedule" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">行程管理</div>
			</a>
		</li>
		<li id="organizationsHeader" class="menu-item" data-function="N2-2">
			<a href="javascript:void(0);" class="menu-link menu-toggle">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Layouts">下級機構</div>
			</a>
			<ul class="menu-sub">
				<li class="menu-item">
					<a id="/organizations" href="/organizations" class="menu-link">
						<div data-i18n="Without menu">下級機構管理</div>
					</a>
				</li>
				<li class="menu-item">
					<a id="/organizations/dashboard" href="/organizations/dashboard" class="menu-link">
						<div data-i18n="Without navbar">機構儀表板</div>
					</a>
				</li>
			</ul>
		</li>
		<li id="practitionersHeader" class="menu-item" data-function="N2-3">
			<a href="javascript:void(0);" class="menu-link menu-toggle">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Layouts">人事管理</div>
			</a>
			<ul class="menu-sub">
				<li class="menu-item">
					<a id="/practitioners" href="/practitioners" class="menu-link">
						<div data-i18n="Without menu">人事總攬</div>
					</a>
				</li>
				<li class="menu-item">
					<a id="/practitioners/newPractitioner" href="/practitioners/newPractitioner" class="menu-link">
						<div data-i18n="Without navbar">新增人員</div>
					</a>
				</li>
			</ul>
		</li>
		<li id="scaleHeader" class="menu-item" data-function="N2-4">
			<a href="javascript:void(0);" class="menu-link menu-toggle">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Layouts">量表管理</div>
			</a>
			<ul class="menu-sub">
				<li class="menu-item">
					<a id="/scale/design" href="/scale/design" class="menu-link">
						<div data-i18n="Without menu">一般量表設計</div>
					</a>
				</li>
				<li class="menu-item">
					<a id="/scale/designScore" href="/scale/designScore" class="menu-link">
						<div data-i18n="Without menu">計分量表設計</div>
					</a>
				</li>
				<li class="menu-item">
					<a id="/scale/assign" href="/scale/assign" class="menu-link">
						<div data-i18n="Without menu">指派與回收</div>
					</a>
				</li>
				<li class="menu-item">
					<a id="/scale/result" href="/scale/result" class="menu-link">
						<div data-i18n="Without menu">量表填答結果</div>
					</a>
				</li>
			</ul>
		</li>
		<% if (manage_oid == 2) { %>
		<li class="menu-item">
			<a id="/messaging" href="/messaging" class="menu-link" data-function="N2-5">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">留言管理</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="N3">
			<span class="menu-header-text">後臺功能</span>
		</li>
		<li class="menu-item" data-function="N3-1">
			<a id="/backend" href="/backend" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">使用者列表</div>
			</a>
		</li>
		<% } %>
		<!-------------- 中化銀髮 -------------->
		<li class="menu-header small text-uppercase" data-function="A">
			<span class="menu-header-text">業務接案</span>
		</li>
		<li class="menu-item" data-function="A-4">
			<a id="/phoneConsultationRecords" href="/phoneConsultationRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">電話諮詢紀錄</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="B">
			<span class="menu-header-text">員工基本資料</span>
		</li>
		<li class="menu-item" data-function="B-1">
			<a id="/employee" href="/employee" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">員工清單</div>
			</a>
		</li>
		<li class="menu-item" data-function="B-1">
			<a id="/statusList" href="/employee/statusList" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">請假及異動</div>
			</a>
		</li>
		<li class="menu-item" data-function="B-1">
			<a id="/healthList" href="/employee/healthList" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">健康項目</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="B">
			<span class="menu-header-text">個案基本資料</span>
		</li>
		<li class="menu-item" data-function="B-2">
			<a id="/client" href="/client" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">個案清單</div>
			</a>
		</li>
		<li class="menu-item" data-function="B-2">
			<a id="/healthListClient" href="/client/healthListClient" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">健康項目</div>
			</a>
		</li>
		<li class="menu-item" data-function="B-2">
			<a id="/adjustMultipleClients" href="/client/adjustMultipleClients" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">批量調整</div>
			</a>
		</li>
		<li class="menu-item" data-function="B-3">
			<a id="/htmlParsing" href="/htmlParsing" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">衛福資料導入</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="D">
			<span class="menu-header-text">服務紀錄</span>
		</li>
		<li class="menu-item" data-function="D-1">
			<a id="/everyDailyServiceRecords" href="/everyDailyServiceRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">D1.每日服務記錄</div>
			</a>
		</li>
		<li class="menu-header small text-uppercase" data-function="D-2">
			<span class="menu-header-text">D2.日常紀錄表單</span>
		</li>
		<li class="menu-item" data-function="D-21">
			<a id="/homeVisitRecords" href="/homeVisitRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">D21.家訪紀錄</div>
			</a>
		</li>
		<li class="menu-item" data-function="D-22">
			<a id="/phoneVisitRecords" href="/phoneVisitRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">D22.電訪紀錄</div>
			</a>
		</li>
		<!-- <li class="menu-item" data-function="D-23">
			<a id="/abnormalServiceRecords" href="/abnormalServiceRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-exclamation-triangle fa-fw"></i>
				<div data-i18n="Analytics">D23.異常服務記錄</div>
			</a>
		</li>
		<li class="menu-item" data-function="D-25">
			<a id="/dailyServiceRecords" href="/dailyServiceRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-calendar-day fa-fw"></i>
				<div data-i18n="Analytics">D25.日常服務記錄</div>
			</a>
		</li>
		<li class="menu-item" data-function="D-26">
			<a id="/suspensionRecords" href="/suspensionRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-pause-circle fa-fw"></i>
				<div data-i18n="Analytics">D26.暫停記錄</div>
			</a>
		</li>
		<li class="menu-item" data-function="D-27">
			<a id="/serviceQuestionnaires" href="/serviceQuestionnaires" class="menu-link">
				<i class="menu-icon tf-icons fas fa-clipboard-list fa-fw"></i>
				<div data-i18n="Analytics">D27.服務問卷</div>
			</a>
		</li>
		<li class="menu-item" data-function="D-29">
			<a id="/caseClosureRecords" href="/caseClosureRecords" class="menu-link">
				<i class="menu-icon tf-icons fas fa-folder-minus fa-fw"></i>
				<div data-i18n="Analytics">D29.結案記錄</div>
			</a>
		</li> -->
		<!-- 此為A單位區域 尚未加入權限控制 -->
		<li class="menu-header small text-uppercase" data-function="UA-1">
			<span class="menu-header-text">A單位專用</span>
		</li>
		<li class="menu-item" data-function="UA-1">
			<a id="/unitA" href="/unitA" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">個案清單</div>
			</a>
		</li>
		<li class="menu-item" data-function="UA-1">
			<a id="/cs100" href="/unitA/cs100" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">CS100管理</div>
			</a>
		</li>
		<li class="menu-item" data-function="UA-1">
			<a id="/aa02" href="/unitA/aa02" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">AA02編輯</div>
			</a>
		</li>
		<li class="menu-item" data-function="UA-1">
			<a id="/template" href="/unitA/template" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">模板管理</div>
			</a>
		</li>
		<!-------------- Sample -------------->
		<!-- <li class="menu-header small text-uppercase" data-function="TE1">
			<span class="menu-header-text">新版權限測試</span>
		</li>
		<li class="menu-item" data-function="TE1-1">
			<a id="#" href="#" class="menu-link">
				<i class="menu-icon tf-icons fas fa-users fa-fw"></i>
				<div data-i18n="Analytics">OOOO</div>
			</a>
		</li> -->
	</ul>

	<script>
		let user_permissions = <%- JSON.stringify(locals.user_permissions || '') %>;
		checkPermission();
		function checkPermission() {
			if(user_permissions === "super") {
				document.querySelectorAll("[data-function]").forEach(function (el) {
					el.style.display = "block";
				});
				return;
			}

			try {
				user_permissions = JSON.parse(user_permissions);

				let functions = user_permissions.functions;
				if(functions && Array.isArray(functions) && functions.length > 0) {
					functions.forEach(function (func) {
						let no = func.no;
						document.querySelectorAll("[data-function='" + no + "']").forEach(function (el) {
							el.style.display = "block";
						});

						let b_no = no.split("-");
						document.querySelectorAll("[data-function='" + b_no[0] + "']").forEach(function (el) {
							el.style.display = "block";
						});
					});
				}
			} catch(e) {}
		}

		function checkActionPermission(no) {
			if(user_permissions === "super") {
				return true;
			}
			
			no = no.split("-");
			// if(no.length !== 2) {
			// 	return false;
			// }

			let action = no.pop();
			no = no.slice(0, no.length).join("-");

			let match = false;
			try {
				let functions = user_permissions.functions;
				if(functions && Array.isArray(functions) && functions.length > 0) {
					let func = functions.find((obj) => obj.no == no);

					if(func) {
						let actions = func.actions;
						if(actions && Array.isArray(actions) && actions.length > 0) {
							match = actions.find((obj) => obj == action) ? true : false;
						}
					}
				}
			} catch(e) {}

			return match;
		}

		setActive();

		function setActive() {
			let find = false;
			let currentURL = window.location.toString();
			let URL = currentURL.split("?")[0];
			let sp = URL.split("/");
			let currentPage = "/" + sp[sp.length - 1].replace("#", "");
			let ul = document.getElementById("slide_ul");
			let items = ul.getElementsByTagName("li");
			for (let i = 1; i < items.length; ++i) {
				let a = items[i].getElementsByTagName("a").length > 0 ? items[i].getElementsByTagName("a")[0] : null;
				if(a) {
					let href = a.getAttribute("href");
					let id = a.getAttribute("id");
					if(href == currentPage || id == currentPage) {
						items[i].setAttribute("class", "menu-item active");
						find = true;
					}
					else {
						items[i].setAttribute("class", "menu-item");
					}
				}
			}

			if(!find) {
				// find ul has class menu-sub
				let sub = ul.getElementsByClassName("menu-sub");
				for(let i = 0; i < sub.length; ++i) {
					let items = sub[i].getElementsByTagName("li");
					for (let j = 0; j < items.length; ++j) {
						let a = items[j].getElementsByTagName("a").length > 0 ? items[j].getElementsByTagName("a")[0] : null;
						if(a) {
							let href = a.getAttribute("href");
							if(href == currentPage) {
								items[j].setAttribute("class", "menu-item active");
								let parent = items[j].parentNode.parentNode;
								parent.setAttribute("class", "menu-item active open");
								find = true;
							} else {
								items[j].setAttribute("class", "menu-item");
							}
						}
					}
				}
			}
		}
	</script>
</aside>