require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_case_closure_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 表單資訊
  \`service_no\` varchar(50) DEFAULT NULL COMMENT '表單編號',
  \`closure_date\` date DEFAULT NULL COMMENT '結案日期',
  \`charge_type\` varchar(10) DEFAULT 'N' COMMENT '費用類別(N=公費, S=自費)',
  
  -- 人員資訊
  \`id_number\` varchar(20) DEFAULT NULL COMMENT '使用者身分證',
  \`user_name\` varchar(100) DEFAULT NULL COMMENT '使用者姓名',
  \`service_type\` varchar(10) DEFAULT NULL COMMENT '服務類別(5=照服, 6=日照, 4=短照, 7=喘息, S=自費方案)',
  \`duty_member_code\` varchar(50) DEFAULT NULL COMMENT '社工/督導代碼',
  \`duty_member_name\` varchar(100) DEFAULT NULL COMMENT '社工/督導姓名',
  \`duty_member2_code\` varchar(50) DEFAULT NULL COMMENT '主責督導代碼',
  \`duty_member2_name\` varchar(100) DEFAULT NULL COMMENT '主責督導姓名',
  \`srv_member_code\` varchar(50) DEFAULT NULL COMMENT '主要服務員代碼',
  \`srv_member_name\` varchar(100) DEFAULT NULL COMMENT '主要服務員姓名',
  
  -- 結案資訊
  \`closure_category\` varchar(100) DEFAULT '其他' COMMENT '結案分類',
  \`closure_reasons\` text DEFAULT NULL COMMENT '結案原因(JSON格式)',
  \`closure_reason_other\` varchar(500) DEFAULT NULL COMMENT '其他結案原因',
  \`closure_description\` text DEFAULT NULL COMMENT '結案說明',
  \`handling_tracking\` text DEFAULT NULL COMMENT '處理/追蹤',
  
  -- 審核狀態
  \`sign_status\` varchar(50) DEFAULT '未審核' COMMENT '審核狀態',
  \`sign_date\` timestamp NULL DEFAULT NULL COMMENT '審核日期',
  \`sign_by\` int(11) DEFAULT NULL COMMENT '審核者ID',
  
  -- 狀態與時間戳記
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_closure_date\` (\`closure_date\`),
  KEY \`idx_id_number\` (\`id_number\`),
  KEY \`idx_service_type\` (\`service_type\`),
  KEY \`idx_duty_member_code\` (\`duty_member_code\`),
  KEY \`idx_duty_member2_code\` (\`duty_member2_code\`),
  KEY \`idx_srv_member_code\` (\`srv_member_code\`),
  KEY \`idx_sign_status\` (\`sign_status\`),
  KEY \`idx_is_delete\` (\`is_delete\`),
  KEY \`idx_created_at\` (\`created_at\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='結案記錄表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_case_closure_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`service_no\`, \`closure_date\`, \`charge_type\`,
  \`id_number\`, \`user_name\`, \`service_type\`,
  \`duty_member_code\`, \`duty_member_name\`, \`duty_member2_code\`, \`duty_member2_name\`,
  \`srv_member_code\`, \`srv_member_name\`,
  \`closure_category\`, \`closure_reasons\`, \`closure_reason_other\`,
  \`closure_description\`, \`handling_tracking\`, \`sign_status\`
) VALUES 
(
  1, 1, 1,
  'CR20250707001', '2025-07-07', 'N',
  'A123456789', '王小明', '5',
  'B1234', '李庭怡', 'B5678', '陳督導',
  'C5678', '陳照服員',
  '其他', '["轉由家屬自行照護", "客戶無使用意願"]', '家屬表示可自行照護',
  '<p>案主家屬經評估後，認為可以自行照護，不再需要居家照顧服務。</p><p><strong>結案原因：</strong></p><ul><li>家屬照護能力充足</li><li>案主狀況穩定</li><li>經濟考量</li></ul>',
  '<p><strong>後續追蹤計畫：</strong></p><p>1. 一個月後電話關懷</p><p>2. 提供緊急聯絡方式</p><p>3. 如有需要可隨時重新申請服務</p>',
  '已審核'
),
(
  1, 1, 1,
  'CR20250706001', '2025-07-06', 'S',
  'B987654321', '李阿嬤', '6',
  'B1234', '李庭怡', 'B5678', '陳督導',
  'C9999', '林照服員',
  '其他', '["轉由日照中心照顧"]', '轉至鄰近日照中心',
  '<p>案主因需要更密集的照護服務，經評估後轉介至日照中心。</p><p><strong>轉介原因：</strong></p><ul><li>需要更專業的復健服務</li><li>社交互動需求</li><li>家屬工作時間考量</li></ul>',
  '<p><strong>轉介安排：</strong></p><p>1. 已聯繫暖時光日照中心</p><p>2. 完成轉介評估</p><p>3. 預計下週開始日照服務</p><p>4. 持續關懷適應狀況</p>',
  '未審核'
);
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建結案記錄表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('結案記錄系統已準備就緒。');
        });
    });
});
