require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const insertTestDataSQL = `
INSERT INTO \`cs_phone_consultation_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`call_date\`, \`contact_person_code\`, \`contact_person_name\`,
  \`apply_name\`, \`apply_gender\`, \`apply_phone\`, \`apply_relationship\`, \`apply_email\`,
  \`duty_member_code\`, \`duty_member_name\`, \`data_source\`,
  \`client_name\`, \`client_id_number\`, \`client_gender\`, \`client_age\`,
  \`address_city\`, \`address_district\`, \`address_detail\`,
  \`current_status\`, \`priority_level\`
) VALUES 
(1, 1, 1,
 '2025-07-07', 'B1234', '李庭怡',
 '王小明', '男', '0912345678', '本人', '<EMAIL>',
 'B1234', '李庭怡', '網路搜尋',
 '王小明', 'A123456789', '男', 65,
 '台北市', '大安區', '仁愛路四段123號',
 'pending', 'normal'),
(1, 1, 1,
 '2025-07-06', 'B1234', '李庭怡',
 '張美麗', '女', '0923456789', '女兒', '<EMAIL>',
 'B5678', '陳督導', '親友介紹',
 '張大華', 'B987654321', '男', 75,
 '台北市', '信義區', '松仁路100號',
 'completed', 'high');
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始插入電話諮詢記錄測試資料...');
    
    // 插入測試資料
    connection.query(insertTestDataSQL, (insertError, insertResults) => {
        connection.release();
        
        if (insertError) {
            console.error('插入測試資料錯誤:', insertError);
            return;
        }
        
        console.log('測試資料插入成功！');
        console.log('電話諮詢記錄系統已準備就緒。');
    });
});
