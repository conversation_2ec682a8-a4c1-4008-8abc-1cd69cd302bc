require('dotenv').config();
const mysqlConnection = require('../mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_home_visit_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊
  \`uid\` int(11) DEFAULT NULL COMMENT '案主ID',
  \`service_date\` date DEFAULT NULL COMMENT '服務日期',
  \`visit_time_start\` time DEFAULT NULL COMMENT '訪視開始時間',
  \`visit_time_end\` time DEFAULT NULL COMMENT '訪視結束時間',
  
  -- 服務人員資訊
  \`service_staff_uid\` varchar(50) DEFAULT NULL COMMENT '服務人員代碼',
  \`service_staff_name\` varchar(100) DEFAULT NULL COMMENT '服務人員姓名',
  \`social_worker_uid\` varchar(50) DEFAULT NULL COMMENT '社工代碼',
  \`social_worker_name\` varchar(100) DEFAULT NULL COMMENT '社工姓名',
  \`supervisor_uid\` varchar(50) DEFAULT NULL COMMENT '督導代碼',
  \`supervisor_name\` varchar(100) DEFAULT NULL COMMENT '督導姓名',
  
  -- 訪視資訊
  \`reason\` text DEFAULT NULL COMMENT '訪視原因',
  \`reason_other\` text DEFAULT NULL COMMENT '其他原因',
  \`status\` varchar(20) DEFAULT 'scheduled' COMMENT '狀態',
  \`visit_location\` varchar(100) DEFAULT NULL COMMENT '訪視地點',
  \`visit_purpose\` text DEFAULT NULL COMMENT '訪視目的',
  \`visit_content\` text DEFAULT NULL COMMENT '訪視內容',
  \`visit_result\` text DEFAULT NULL COMMENT '訪視結果',
  \`next_visit_date\` date DEFAULT NULL COMMENT '下次訪視日期',
  \`notes\` text DEFAULT NULL COMMENT '備註',
  
  -- 評估項目
  \`target\` text DEFAULT NULL COMMENT '訪視對象',
  \`service_item\` text DEFAULT NULL COMMENT '服務項目',
  \`service_item2\` text DEFAULT NULL COMMENT '服務項目2',
  \`physical_status\` text DEFAULT NULL COMMENT '身體狀況',
  \`emotion_status\` text DEFAULT NULL COMMENT '情緒狀況',
  \`social_status\` text DEFAULT NULL COMMENT '社會狀況',
  \`home_environment\` text DEFAULT NULL COMMENT '居家環境',
  \`family_care\` text DEFAULT NULL COMMENT '家庭照顧',
  \`caregiver_status\` text DEFAULT NULL COMMENT '照顧者狀況',
  \`service_attitude\` text DEFAULT NULL COMMENT '服務態度',
  \`service_attendance\` text DEFAULT NULL COMMENT '服務出勤',
  \`service_execution\` text DEFAULT NULL COMMENT '服務執行',
  \`follow_item\` text DEFAULT NULL COMMENT '後續處理',
  
  -- 其他欄位
  \`physical_status_other\` text DEFAULT NULL COMMENT '身體狀況其他',
  \`physical_status_remark\` text DEFAULT NULL COMMENT '身體狀況備註',
  \`emotion_status_other\` text DEFAULT NULL COMMENT '情緒狀況其他',
  \`emotion_status_remark\` text DEFAULT NULL COMMENT '情緒狀況備註',
  \`social_status_other\` text DEFAULT NULL COMMENT '社會狀況其他',
  \`social_status_remark\` text DEFAULT NULL COMMENT '社會狀況備註',
  \`home_environment_other\` text DEFAULT NULL COMMENT '居家環境其他',
  \`home_environment_remark\` text DEFAULT NULL COMMENT '居家環境備註',
  \`family_care_other\` text DEFAULT NULL COMMENT '家庭照顧其他',
  \`family_care_remark\` text DEFAULT NULL COMMENT '家庭照顧備註',
  \`caregiver_status_other\` text DEFAULT NULL COMMENT '照顧者狀況其他',
  \`caregiver_status_remark\` text DEFAULT NULL COMMENT '照顧者狀況備註',
  \`service_attitude_other\` text DEFAULT NULL COMMENT '服務態度其他',
  \`service_attitude_remark\` text DEFAULT NULL COMMENT '服務態度備註',
  \`service_attendance_other\` text DEFAULT NULL COMMENT '服務出勤其他',
  \`service_attendance_remark\` text DEFAULT NULL COMMENT '服務出勤備註',
  \`service_execution_other\` text DEFAULT NULL COMMENT '服務執行其他',
  \`service_execution_remark\` text DEFAULT NULL COMMENT '服務執行備註',
  
  -- 狀態與時間戳記
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`updated_by\` int(11) DEFAULT NULL COMMENT '更新者ID',
  \`updated_date\` timestamp NULL DEFAULT NULL COMMENT '更新時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_uid\` (\`uid\`),
  KEY \`idx_service_date\` (\`service_date\`),
  KEY \`idx_status\` (\`status\`),
  KEY \`idx_is_delete\` (\`is_delete\`),
  KEY \`idx_created_at\` (\`created_at\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家訪記錄表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_home_visit_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`, \`uid\`, \`service_date\`, 
  \`visit_time_start\`, \`visit_time_end\`, \`service_staff_uid\`, \`service_staff_name\`,
  \`social_worker_uid\`, \`social_worker_name\`, \`supervisor_name\`, \`reason\`, 
  \`reason_other\`, \`status\`, \`visit_location\`, \`visit_purpose\`, \`visit_content\`,
  \`visit_result\`, \`notes\`
) VALUES 
(2, 1, 1227, 1227, '2025-07-28', 
 '09:00:00', '11:00:00', 'A123', '陳督導',
 'A123', '陳督導', '李督導', '達成，繼續服務', 
 '定期追蹤服務狀況', 'completed', '案家', '服務品質評估', '進行服務滿意度調查及需求評估',
 '案主對目前服務表示滿意，建議持續現有服務', '下次訪視預計一個月後進行');
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建家訪記錄表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('家訪記錄系統已準備就緒。');
        });
    });
});
