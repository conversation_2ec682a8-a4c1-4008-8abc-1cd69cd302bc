require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_phone_consultation_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊
  \`call_date\` date DEFAULT NULL COMMENT '來電日期',
  \`contact_person_code\` varchar(50) DEFAULT NULL COMMENT '接聽人員代碼',
  \`contact_person_name\` varchar(100) DEFAULT NULL COMMENT '接聽人員姓名',
  
  -- 申請人資訊
  \`apply_name\` varchar(100) DEFAULT NULL COMMENT '申請人姓名',
  \`apply_gender\` varchar(10) DEFAULT NULL COMMENT '申請人性別',
  \`apply_phone\` varchar(50) DEFAULT NULL COMMENT '申請人電話',
  \`apply_relationship\` varchar(50) DEFAULT NULL COMMENT '與案主關係',
  \`apply_relationship_other\` varchar(100) DEFAULT NULL COMMENT '其他關係說明',
  \`apply_email\` varchar(100) DEFAULT NULL COMMENT '申請人Email',
  
  -- 轉介資訊
  \`duty_member_code\` varchar(50) DEFAULT NULL COMMENT '主責客專代碼',
  \`duty_member_name\` varchar(100) DEFAULT NULL COMMENT '主責客專姓名',
  \`data_source\` varchar(50) DEFAULT NULL COMMENT '資料來源',
  \`data_source_other\` varchar(100) DEFAULT NULL COMMENT '其他資料來源說明',
  \`referral_organization\` varchar(100) DEFAULT NULL COMMENT '轉介機構',
  \`referral_department\` varchar(100) DEFAULT NULL COMMENT '轉介部門',
  
  -- 案主資訊
  \`client_name\` varchar(100) DEFAULT NULL COMMENT '案主姓名',
  \`client_id_number\` varchar(20) DEFAULT NULL COMMENT '案主身分證',
  \`client_gender\` varchar(10) DEFAULT NULL COMMENT '案主性別',
  \`client_age\` int(11) DEFAULT NULL COMMENT '案主年齡',
  \`client_height\` float DEFAULT NULL COMMENT '案主身高',
  \`client_weight\` float DEFAULT NULL COMMENT '案主體重',
  \`client_phone\` varchar(50) DEFAULT NULL COMMENT '案主電話',
  
  -- 地址資訊
  \`address_zipcode\` varchar(10) DEFAULT NULL COMMENT '郵遞區號',
  \`address_city\` varchar(50) DEFAULT NULL COMMENT '縣市',
  \`address_district\` varchar(50) DEFAULT NULL COMMENT '鄉鎮市區',
  \`address_detail\` varchar(255) DEFAULT NULL COMMENT '詳細地址',
  
  -- 健康狀況
  \`living_status\` varchar(50) DEFAULT NULL COMMENT '居住狀況',
  \`languages\` text DEFAULT NULL COMMENT '使用語言(JSON)',
  \`languages_other\` varchar(100) DEFAULT NULL COMMENT '其他語言說明',
  \`tube_placement\` text DEFAULT NULL COMMENT '管路置放(JSON)',
  \`consciousness_status\` varchar(50) DEFAULT NULL COMMENT '意識狀態',
  \`diseases_surgery\` text DEFAULT NULL COMMENT '疾病/手術(JSON)',
  
  -- 行動能力
  \`mobility_ability\` varchar(50) DEFAULT NULL COMMENT '行動能力',
  \`transfer_needs\` varchar(50) DEFAULT NULL COMMENT '轉位需求',
  \`mobility_restrictions\` text DEFAULT NULL COMMENT '行動限制(JSON)',
  
  -- 服務需求
  \`service_gender_preference\` varchar(20) DEFAULT NULL COMMENT '服務員性別偏好',
  \`service_time_status\` varchar(50) DEFAULT NULL COMMENT '服務時間狀態',
  \`daily_hours\` int(11) DEFAULT NULL COMMENT '每日時數',
  \`service_time_period\` text DEFAULT NULL COMMENT '服務時段(JSON)',
  \`service_days\` text DEFAULT NULL COMMENT '服務天數(JSON)',
  \`service_items\` text DEFAULT NULL COMMENT '服務項目(JSON)',
  \`service_items_other\` varchar(255) DEFAULT NULL COMMENT '其他服務項目說明',
  
  -- 諮詢內容
  \`consultation_content\` text DEFAULT NULL COMMENT '諮詢內容',
  \`consultation_response\` text DEFAULT NULL COMMENT '回覆內容',
  \`special_notes\` text DEFAULT NULL COMMENT '特殊備註',
  
  -- 狀態追蹤
  \`current_status\` varchar(50) DEFAULT 'pending' COMMENT '目前狀態',
  \`priority_level\` varchar(20) DEFAULT 'normal' COMMENT '優先等級',
  \`follow_up_required\` tinyint(1) DEFAULT 0 COMMENT '需要追蹤',
  \`follow_up_date\` date DEFAULT NULL COMMENT '追蹤日期',
  \`follow_up_notes\` text DEFAULT NULL COMMENT '追蹤備註',
  
  -- 系統欄位
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_by\` int(11) DEFAULT NULL COMMENT '更新者ID',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_call_date\` (\`call_date\`),
  KEY \`idx_contact_person_code\` (\`contact_person_code\`),
  KEY \`idx_client_id_number\` (\`client_id_number\`),
  KEY \`idx_address_city\` (\`address_city\`),
  KEY \`idx_address_district\` (\`address_district\`),
  KEY \`idx_current_status\` (\`current_status\`),
  KEY \`idx_priority_level\` (\`priority_level\`),
  KEY \`idx_follow_up_required\` (\`follow_up_required\`),
  KEY \`idx_follow_up_date\` (\`follow_up_date\`),
  KEY \`idx_is_delete\` (\`is_delete\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='電話諮詢記錄表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_phone_consultation_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`call_date\`, \`contact_person_code\`, \`contact_person_name\`,
  \`apply_name\`, \`apply_gender\`, \`apply_phone\`, \`apply_relationship\`, \`apply_email\`,
  \`duty_member_code\`, \`duty_member_name\`, \`data_source\`,
  \`client_name\`, \`client_id_number\`, \`client_gender\`, \`client_age\`,
  \`address_city\`, \`address_district\`, \`address_detail\`,
  \`consultation_content\`, \`consultation_response\`, \`current_status\`, \`priority_level\`
) VALUES 
(1, 1, 1,
 '2025-07-07', 'B1234', '李庭怡',
 '王小明', '男', '0912345678', '本人', '<EMAIL>',
 'B1234', '李庭怡', '網路搜尋',
 '王小明', 'A123456789', '男', 65,
 '台北市', '大安區', '仁愛路四段123號',
 '詢問居家照顧服務的相關資訊，包括收費標準和服務內容。', '已提供基本說明並安排後續訪視。', 'pending', 'normal'),
(1, 1, 1,
 '2025-07-06', 'B1234', '李庭怡',
 '張美麗', '女', '0923456789', '女兒', '<EMAIL>',
 'B5678', '陳督導', '親友介紹',
 '張大華', 'B987654321', '男', 75,
 '台北市', '信義區', '松仁路100號',
 '為父親詢問長照服務，父親行動不便需要協助。', '已說明長照2.0補助方案，並安排評估訪視。', 'completed', 'high');
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建電話諮詢記錄表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('電話諮詢記錄系統已準備就緒。');
        });
    });
});
