# 日間/夜間服務時數計算功能說明

## 概述
根據您的需求，我們已經實現了基於服務時間範圍 (time1 到 time2) 來計算日間18點前和夜間18點後服務時數的功能。

## 功能說明

### 計算邏輯
- **分界點**: 18:00 (下午6點)
- **日間時數**: 18:00 之前的服務時間
- **夜間時數**: 18:00 之後的服務時間
- **計算單位**: 以分鐘為基礎，最後除以60轉換為小時
- **顯示格式**: 小數點後兩位 (例如: 2.50)

### 計算情境

#### 1. 完全在日間 (18:00前)
- **範例**: 08:00 - 12:00
- **日間時數**: 4.00 小時
- **夜間時數**: 0.00 小時

#### 2. 完全在夜間 (18:00後)
- **範例**: 19:00 - 22:00
- **日間時數**: 0.00 小時
- **夜間時數**: 3.00 小時

#### 3. 跨越18:00
- **範例**: 16:00 - 20:00
- **日間時數**: 2.00 小時 (16:00-18:00)
- **夜間時數**: 2.00 小時 (18:00-20:00)

#### 4. 剛好18:00開始
- **範例**: 18:00 - 21:00
- **日間時數**: 0.00 小時
- **夜間時數**: 3.00 小時

#### 5. 剛好18:00結束
- **範例**: 15:00 - 18:00
- **日間時數**: 3.00 小時
- **夜間時數**: 0.00 小時

## 實作細節

### JavaScript 函數

#### 1. timeToMinutes(timeString)
```javascript
function timeToMinutes(timeString) {
    if (!timeString) return 0;
    
    const parts = timeString.split(':');
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    
    return hours * 60 + minutes;
}
```
- **功能**: 將時間字串 (HH:MM 或 HH:MM:SS) 轉換為分鐘數
- **輸入**: "16:30" 或 "16:30:00"
- **輸出**: 990 (16*60 + 30)

#### 2. calculateDaytimeHours(time1, time2)
```javascript
function calculateDaytimeHours(time1, time2) {
    if (!time1 || !time2) return '0.00';
    
    const startMinutes = timeToMinutes(time1);
    const endMinutes = timeToMinutes(time2);
    const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘
    
    let daytimeMinutes = 0;
    
    if (endMinutes <= cutoffMinutes) {
        // 完全在18點前
        daytimeMinutes = endMinutes - startMinutes;
    } else if (startMinutes < cutoffMinutes) {
        // 跨越18點
        daytimeMinutes = cutoffMinutes - startMinutes;
    }
    
    return (daytimeMinutes / 60).toFixed(2);
}
```

#### 3. calculateNighttimeHours(time1, time2)
```javascript
function calculateNighttimeHours(time1, time2) {
    if (!time1 || !time2) return '0.00';
    
    const startMinutes = timeToMinutes(time1);
    const endMinutes = timeToMinutes(time2);
    const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘
    
    let nighttimeMinutes = 0;
    
    if (startMinutes >= cutoffMinutes) {
        // 完全在18點後
        nighttimeMinutes = endMinutes - startMinutes;
    } else if (endMinutes > cutoffMinutes) {
        // 跨越18點
        nighttimeMinutes = endMinutes - cutoffMinutes;
    }
    
    return (nighttimeMinutes / 60).toFixed(2);
}
```

### 前端顯示更新

在 `views/dailyServiceRecords/index.ejs` 的 displayRecords 函數中：

```javascript
<td>${calculateDaytimeHours(record.time1, record.time2)}</td>
<td>${calculateNighttimeHours(record.time1, record.time2)}</td>
```

## 測試案例

| 時間範圍 | 描述 | 日間時數 | 夜間時數 | 總時數 |
|---------|------|---------|---------|--------|
| 08:00 - 12:00 | 完全在日間 | 4.00 | 0.00 | 4.00 |
| 19:00 - 22:00 | 完全在夜間 | 0.00 | 3.00 | 3.00 |
| 16:00 - 20:00 | 跨越18點 | 2.00 | 2.00 | 4.00 |
| 17:30 - 18:30 | 跨越18點 | 0.50 | 0.50 | 1.00 |
| 18:00 - 21:00 | 剛好18點開始 | 0.00 | 3.00 | 3.00 |
| 15:00 - 18:00 | 剛好18點結束 | 3.00 | 0.00 | 3.00 |
| 14:30 - 19:45 | 跨越18點長時間 | 3.50 | 1.75 | 5.25 |

## 特色功能

### 1. 精確計算
- 以分鐘為基礎進行計算，確保精確度
- 支援跨越18:00的複雜時間範圍

### 2. 容錯處理
- 當 time1 或 time2 為空時，返回 "0.00"
- 支援 HH:MM 和 HH:MM:SS 兩種時間格式

### 3. 格式化顯示
- 統一顯示為小數點後兩位
- 便於閱讀和統計

### 4. 邏輯完整
- 涵蓋所有可能的時間範圍情境
- 確保日間時數 + 夜間時數 = 總服務時數

## 使用方式

1. 進入日常服務記錄列表頁面
2. 執行搜尋查詢
3. 觀察表格中的「日間18點前」和「夜間18點後」欄位
4. 系統會自動根據 time1 和 time2 計算並顯示對應的時數

## 技術優勢

- **即時計算**: 在前端即時計算，無需額外的資料庫查詢
- **高效能**: 純 JavaScript 計算，響應速度快
- **可維護**: 邏輯清晰，易於理解和修改
- **可擴展**: 可以輕鬆調整分界時間或添加其他時段

## 完成狀態
✅ 時間轉換函數實作
✅ 日間時數計算邏輯
✅ 夜間時數計算邏輯
✅ 跨越18點的複雜情境處理
✅ 前端顯示整合
✅ 測試案例驗證

功能已完成並可正常使用！
