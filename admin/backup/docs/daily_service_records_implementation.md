# 每日服務記錄系統實作說明

## 概述
根據 D1.txt 的內容，我們完成了每日服務記錄系統的實作，包含查詢和新增功能。

## 實作內容

### 1. 資料庫結構
- **表格名稱**: `cs_daily_service_records`
- **主要欄位**:
  - `company_id`: 所屬單位
  - `user_id`: 使用者ID
  - `user_name`: 使用者姓名
  - `srv_member_code`: 服務人員代碼
  - `srv_member_name`: 服務人員姓名
  - `service_date`: 服務日期
  - `service_time_start`: 服務開始時間
  - `service_time_end`: 服務結束時間
  - `service_hours`: 服務時數
  - `service_minutes`: 服務分鐘數
  - `traffic_minutes`: 交通分鐘數
  - `service_type`: 服務類別
  - `service_items`: 服務項目
  - `service_status`: 狀態 (N=正常, S=自費, M=未遇, Z=預定, C=取消, R=休息)
  - `is_transfer`: 是否轉場 (1=轉場, 0=不轉場)
  - `remarks`: 備註說明

### 2. 後端實作

#### 路由文件 (`routes/dailyServiceRecords.js`)
- **GET /**: 列表頁面，支援多種查詢條件
- **GET /create**: 新增頁面
- **GET /edit/:id**: 編輯頁面
- **POST /create**: 新增記錄
- **POST /update/:id**: 更新記錄
- **DELETE /:id**: 刪除記錄
- **GET /data/:id**: AJAX 獲取記錄數據

#### Repository 文件 (`repository/dailyServiceRecords.js`)
- `getRecords()`: 查詢記錄列表，支援分頁和篩選
- `getRecordById()`: 根據ID獲取單筆記錄
- `createRecord()`: 新增記錄
- `updateRecord()`: 更新記錄
- `deleteRecord()`: 軟刪除記錄

### 3. 前端實作

#### 列表頁面 (`views/dailyServiceRecords/index.ejs`)
**查詢條件**:
- 所屬單位
- 使用者ID
- 服務年月
- 督導類型
- 服務日期範圍
- 服務人員類型
- 狀態
- 服務類別
- 進階查詢條件：是否獨居、身份別、通訊區域、服務代碼關鍵字、服務時段

**表格顯示欄位**:
- 檢視/功能按鈕
- 服務日期
- 使用者
- 服務人員
- 服務時間
- 服務類別
- 狀態
- 服務時數
- 交通時間
- 服務項目
- 備註說明
- 建檔日/異動日

#### 新增/編輯頁面 (`views/dailyServiceRecords/record_new.ejs`)
**表單設計**:
- 完全按照 D1.txt 的 HTML 結構設計
- 使用表格樣式的表單佈局
- 包含必填欄位標示 (紅色星號)
- 自動計算服務時數功能
- 服務項目選擇功能
- 單選按鈕群組 (狀態、是否轉場)

**主要功能**:
1. **所屬單位選擇**: 下拉選單，預設台北服務站
2. **使用者輸入**: ID輸入 + 自動查詢姓名 (只讀)
3. **服務人員輸入**: 代碼輸入 + 自動查詢姓名 (只讀)
4. **服務時段**: 日期 + 時間範圍，自動計算時數
5. **服務類別**: 下拉選單 (照服、日照、短照、喘息、自費方案)
6. **服務項目**: 文字輸入 + 選擇按鈕
7. **狀態選擇**: 單選按鈕 (正常、自費、未遇、預定、取消、休息)
8. **轉場選擇**: 單選按鈕 (轉場、不轉場)
9. **備註說明**: 多行文字輸入

### 4. 特色功能

#### 自動計算時數
- 當輸入開始和結束時間時，自動計算服務時數和分鐘數
- 支援小時和分鐘兩種顯示方式

#### 服務項目選擇
- 提供常用服務項目的選擇對話框
- 支援多選功能
- 包含身體清潔、協助沐浴、協助更衣等標準服務項目

#### 表單驗證
- 必填欄位檢查
- 時間邏輯驗證 (結束時間必須大於開始時間)
- 友善的錯誤提示

#### 響應式設計
- 支援桌面和行動裝置
- 表格在小螢幕上自動調整佈局

### 5. 檔案結構
```
admin/
├── routes/dailyServiceRecords.js          # 路由控制器
├── repository/dailyServiceRecords.js      # 資料存取層
├── views/dailyServiceRecords/
│   ├── index.ejs                         # 列表頁面
│   └── record_new.ejs                    # 新增/編輯頁面
├── sql/create_daily_service_records.sql  # 資料庫結構
└── docs/daily_service_records_implementation.md  # 說明文檔
```

### 6. 使用方式

#### 訪問頁面
- 列表頁面: `http://localhost:3000/dailyServiceRecords`
- 新增頁面: `http://localhost:3000/dailyServiceRecords/create`
- 編輯頁面: `http://localhost:3000/dailyServiceRecords/edit/{id}`

#### 操作流程
1. 進入列表頁面，設定查詢條件後點擊「查詢」
2. 點擊「新增記錄」進入新增頁面
3. 填寫必填欄位 (標有紅色星號)
4. 系統會自動計算服務時數
5. 可使用「選擇」按鈕選擇服務項目
6. 點擊「儲存」完成新增

### 7. 技術特點
- **前端**: EJS 模板引擎、jQuery、SweetAlert2
- **後端**: Node.js、Express.js
- **資料庫**: MySQL
- **樣式**: 自定義 CSS，符合原系統風格
- **功能**: AJAX 非同步處理、表單驗證、自動計算

### 8. 注意事項
- 使用者姓名和服務人員姓名欄位為只讀，需要透過代碼查詢
- 服務時數會根據時間範圍自動計算
- 所有必填欄位都有適當的驗證
- 支援軟刪除，不會真正刪除資料

## 完成狀態
✅ 資料庫結構設計與建立
✅ 後端 API 開發
✅ 前端頁面開發
✅ 表單驗證功能
✅ 自動計算功能
✅ 服務項目選擇功能
✅ 響應式設計
✅ 測試與除錯

系統已完成並可正常運行。
