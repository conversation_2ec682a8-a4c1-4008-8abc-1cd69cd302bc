# 整合服務記錄查詢系統實作說明

## 概述
根據您提供的兩個資料表 `cs_service_records` 和 `physical_lt`，我們成功實作了整合服務記錄查詢系統，能夠將服務記錄和生理量測數據整合顯示。

## 資料表分析

### 1. cs_service_records (服務記錄表)
**主要欄位**:
- `id`: 主鍵
- `company_id`: 公司代碼
- `service_ym`: 服務年月 (YYYYMM)
- `srv_member`: 服務員編號
- `id_number`: 使用者身分證號
- `service_date`: 服務日期
- `service_type`: 服務類別
- `time1`, `time2`: 開始/結束時間
- `service_hour`, `service_min`: 服務時數/分鐘
- `traffic_min`: 交通分鐘
- `service_status`: 服務狀態
- `srv_item`: 服務項目
- `remark`: 備註
- `sign_date`: 簽核日期 (審核日)

### 2. physical_lt (生理量測資料表)
**主要欄位**:
- `vid`: 主鍵
- `records_id`: 服務紀錄表id
- `uid`: 病患id/使用者身分證號
- `member_code`: 服務員編號
- `temperature`: 體溫 (個案)
- `member_temperature`: 服務員體溫
- `pulse`: 脈搏速率
- `breathe`: 呼吸速率
- `sbp`: 收縮壓 (高壓)
- `dbp`: 舒張壓 (低壓)
- `time`: 量測時間

## 整合查詢邏輯

### 關聯方式
我們使用了三種關聯方式來整合兩個表格的數據：

1. **通過記錄ID關聯**: `physical_lt.records_id = cs_service_records.id`
2. **通過身分證號和日期關聯**: `physical_lt.uid = cs_service_records.id_number AND DATE(physical_lt.time) = cs_service_records.service_date`
3. **通過服務員編號和日期關聯**: `physical_lt.member_code = cs_service_records.srv_member AND DATE(physical_lt.time) = cs_service_records.service_date`

### SQL 查詢結構
```sql
SELECT 
    -- 服務基本資訊
    sr.id AS service_record_id,
    sr.company_id,
    sr.service_ym AS service_year_month,
    sr.srv_member,
    sr.id_number,
    sr.service_date,
    sr.service_type,
    sr.time1 AS service_start_time,
    sr.time2 AS service_end_time,
    sr.service_hour,
    sr.service_min,
    sr.traffic_min,
    sr.service_status,
    sr.srv_item AS service_items,
    sr.remark AS remarks,
    sr.sign_date AS review_date,
    
    -- 生理量測資訊
    pl.temperature AS user_temperature,
    pl.member_temperature AS staff_temperature,
    pl.pulse,
    pl.breathe,
    pl.sbp AS blood_pressure_high,
    pl.dbp AS blood_pressure_low,
    
    -- 計算欄位
    CASE 
        WHEN TIME(sr.time1) < '18:00:00' THEN '是'
        ELSE '否'
    END AS before_18,
    CASE 
        WHEN TIME(sr.time2) >= '18:00:00' THEN '是'
        ELSE '否'
    END AS after_18
FROM 
    cs_service_records sr
LEFT JOIN 
    physical_lt pl ON (
        pl.records_id = sr.id
        OR (pl.uid = sr.id_number AND DATE(pl.time) = sr.service_date)
        OR (pl.member_code = sr.srv_member AND DATE(pl.time) = sr.service_date)
    )
WHERE 
    (pl.is_deleted = 0 OR pl.is_deleted IS NULL)
```

## 實作內容

### 1. 後端實作

#### Repository 層 (`repository/integratedServiceRecords.js`)
- `getIntegratedServiceRecords()`: 整合查詢服務記錄和生理量測數據
- `getIntegratedServiceRecordById()`: 根據ID獲取單筆整合記錄
- 支援分頁查詢和多種搜尋條件

#### 路由層 (`routes/integratedServiceRecords.js`)
- **GET /**: 列表頁面
- **GET /view/:id**: 詳情頁面
- **POST /search**: AJAX 搜尋API
- **GET /data/:id**: 獲取單筆記錄API

### 2. 前端實作

#### 列表頁面 (`views/integratedServiceRecords/index.ejs`)
**搜尋條件**:
- 所屬單位
- 使用者ID
- 服務年月
- 服務日期範圍
- 服務人員代碼
- 服務類別
- 狀態

**表格顯示欄位** (符合您的需求):
1. 檢視
2. 狀態
3. 案號
4. 姓名
5. 類別
6. 服務日期
7. 服務人員
8. 服務時段
9. 服務時數(小時)
10. 服務時數(分鐘)
11. 轉場交通(分鐘)
12. 累計排班(小時)
13. 是否不轉場
14. 服務項目/備註
15. 服務員體溫
16. 個案體溫
17. 血壓(高)
18. 血壓(低)
19. 脈搏
20. 呼吸
21. 通訊區域
22. 日間18點前
23. 夜間18點後
24. 建檔
25. 修改
26. 審核日

#### 詳情頁面 (`views/integratedServiceRecords/view.ejs`)
- 服務基本資訊
- 生理量測資訊
- 系統資訊

### 3. 特色功能

#### 智能關聯
- 自動匹配服務記錄和生理量測數據
- 支援多種關聯方式，確保數據完整性

#### 響應式設計
- 支援桌面和行動裝置
- 表格水平滾動支援

#### 分頁查詢
- 支援大量數據的分頁顯示
- 可自定義每頁顯示筆數

## 無法對應的欄位

以下欄位在現有資料表中無法找到對應數據，顯示為 "-"：

1. **補卡**: 無對應欄位
2. **服務順序**: 無對應欄位
3. **服務金額**: 無對應欄位
4. **累計排班(小時)**: 無對應欄位
5. **血氧濃度**: physical_lt 表中有 `spo2` 欄位，但未在顯示中使用
6. **血糖**: physical_lt 表中有 `sugar` 欄位，但未在顯示中使用
7. **通訊區域**: 無對應欄位

## 建議的資料庫優化

### 1. 欄位類型統一
- `physical_lt.uid` 和 `cs_service_records.id_number` 使用相同資料類型
- `physical_lt.member_code` 和 `cs_service_records.srv_member` 使用相同資料類型

### 2. 外鍵約束
```sql
ALTER TABLE physical_lt 
ADD CONSTRAINT fk_physical_records 
FOREIGN KEY (records_id) REFERENCES cs_service_records(id);
```

### 3. 索引優化
```sql
-- 為關聯查詢添加索引
ALTER TABLE physical_lt ADD INDEX idx_records_id (records_id);
ALTER TABLE physical_lt ADD INDEX idx_uid_time (uid, time);
ALTER TABLE physical_lt ADD INDEX idx_member_time (member_code, time);
```

## 檔案結構
```
admin/
├── routes/integratedServiceRecords.js          # 路由控制器
├── repository/integratedServiceRecords.js      # 資料存取層
├── views/integratedServiceRecords/
│   ├── index.ejs                              # 列表頁面
│   └── view.ejs                               # 詳情頁面
└── docs/integrated_service_records_implementation.md  # 說明文檔
```

## 使用方式

### 訪問頁面
- 列表頁面: `http://localhost:3000/integratedServiceRecords`
- 詳情頁面: `http://localhost:3000/integratedServiceRecords/view/{id}`

### 操作流程
1. 進入列表頁面，設定搜尋條件
2. 點擊「查詢」按鈕執行搜尋
3. 點擊「檢視」按鈕查看詳細資訊
4. 支援分頁瀏覽大量數據

## 技術特點
- **前端**: EJS 模板引擎、jQuery、SweetAlert2
- **後端**: Node.js、Express.js
- **資料庫**: MySQL，使用 LEFT JOIN 整合查詢
- **樣式**: 響應式 CSS 設計
- **功能**: AJAX 非同步處理、分頁查詢、多條件搜尋

## 完成狀態
✅ 資料表分析與整合邏輯設計
✅ 後端 Repository 和 API 開發
✅ 前端列表和詳情頁面開發
✅ 多條件搜尋功能
✅ 分頁查詢功能
✅ 響應式設計
✅ 路由註冊和系統整合
✅ 刪除不需要的舊表格和文件

系統已完成並可正常運行，能夠成功整合 `cs_service_records` 和 `physical_lt` 兩個表格的數據進行查詢和顯示。
