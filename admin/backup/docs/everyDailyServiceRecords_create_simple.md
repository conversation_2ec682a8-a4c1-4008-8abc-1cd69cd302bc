# 每日服務記錄簡化新增頁面實作說明

## 概述
根據您的要求，我已經創建了一個簡化的新增頁面 `everyDailyServiceRecords/create.ejs`，完全按照您提供的 HTML 結構（ID: 25873791-3eb5-4d91-846e-7b7b942d4e69）來設計，移除了不需要的欄位，不使用分大類的設計。

## 實作內容

### 1. 新增頁面 (`views/everyDailyServiceRecords/create.ejs`)

#### 頁面結構
- **簡潔設計**: 不使用複雜的卡片分類，直接使用表格布局
- **符合原始HTML**: 完全按照您提供的HTML結構設計
- **響應式**: 支援桌面和行動裝置

#### 表單欄位

**基本資訊表格**:
```html
<table>
    <tr>
        <td class="title-cell">所屬單位：</td>
        <td>
            <select id="company_id" name="company_id">
                <option value="B1691" selected>台北服務站</option>
                <!-- 其他選項 -->
            </select>
        </td>
    </tr>
    <tr>
        <td class="title-cell">
            <span class="required">*</span>使用者：
        </td>
        <td>
            <input type="text" id="id_number" name="id_number" placeholder="身分證號" required>
            <input type="text" id="user_name" name="user_name" readonly class="readonly-input">
        </td>
    </tr>
    <tr>
        <td class="title-cell">
            <span class="required">*</span>服務人員：
        </td>
        <td>
            <input type="text" id="srv_member" name="srv_member" placeholder="服務人員代碼" required>
            <input type="text" id="srv_member_name" name="srv_member_name" readonly class="readonly-input">
        </td>
    </tr>
    <tr>
        <td class="title-cell">
            <span class="required">*</span>服務時段：
        </td>
        <td>
            <input type="date" id="service_date" name="service_date" required>
            時間：
            <input type="time" id="time1" name="time1" required>
            至
            <input type="time" id="time2" name="time2" required>
            共計
            <input type="text" id="service_hour" name="service_hour" readonly value="0"> 小時，或
            <input type="text" id="service_min" name="service_min" readonly value="0"> 分鐘，交通
            <input type="text" id="traffic_min" name="traffic_min" value="0"> 分鐘
        </td>
    </tr>
</table>
```

**服務詳細資訊表格**:
```html
<table>
    <tr>
        <td class="title-cell">
            <span class="required">*</span>服務類別：
        </td>
        <td>
            <select id="service_type" name="service_type" required>
                <option value="5">照服</option>
                <option value="6">日照</option>
                <option value="4">短照</option>
                <option value="7">喘息</option>
                <option value="S">自費方案</option>
            </select>
        </td>
    </tr>
    <tr>
        <td class="title-cell">服務項目：</td>
        <td>
            <textarea id="srv_item" name="srv_item" placeholder="請輸入服務項目"></textarea>
            <button type="button" onclick="openServiceItemSelector()">選擇</button>
        </td>
    </tr>
</table>
```

**狀態和設定表格**:
```html
<table>
    <tr>
        <td class="title-cell">狀態：</td>
        <td>
            <input type="radio" name="service_status" value="N" checked> 正常
            <input type="radio" name="service_status" value="S"> 自費
            <input type="radio" name="service_status" value="M"> 未遇
            <input type="radio" name="service_status" value="Z"> 預定
            <input type="radio" name="service_status" value="C"> 取消
            <input type="radio" name="service_status" value="R"> 休息
        </td>
    </tr>
    <tr>
        <td class="title-cell">是否轉場：</td>
        <td>
            <input type="radio" name="traffic_times" value="1" checked> 轉場
            <input type="radio" name="traffic_times" value="0"> 不轉場
        </td>
    </tr>
    <tr>
        <td class="title-cell">備註說明：</td>
        <td>
            <textarea id="remark" name="remark" placeholder="請輸入備註說明"></textarea>
        </td>
    </tr>
</table>
```

### 2. 欄位對應

| 顯示名稱 | 欄位名稱 | 類型 | 必填 | 說明 |
|----------|----------|------|------|------|
| 所屬單位 | company_id | select | 否 | 預設台北服務站 |
| 使用者 | id_number | text | 是 | 身分證號 |
| 使用者姓名 | user_name | text | 否 | 自動填入，唯讀 |
| 服務人員 | srv_member | text | 是 | 服務人員代碼 |
| 服務人員姓名 | srv_member_name | text | 否 | 自動填入，唯讀 |
| 服務日期 | service_date | date | 是 | 預設今天 |
| 開始時間 | time1 | time | 是 | 服務開始時間 |
| 結束時間 | time2 | time | 是 | 服務結束時間 |
| 服務時數(小時) | service_hour | text | 否 | 自動計算，唯讀 |
| 服務時數(分鐘) | service_min | text | 否 | 自動計算，唯讀 |
| 交通時間 | traffic_min | text | 否 | 交通分鐘數 |
| 服務類別 | service_type | select | 是 | 5=照服, 6=日照, 4=短照, 7=喘息, S=自費方案 |
| 服務項目 | srv_item | textarea | 否 | 服務項目描述 |
| 狀態 | service_status | radio | 否 | N=正常, S=自費, M=未遇, Z=預定, C=取消, R=休息 |
| 是否轉場 | traffic_times | radio | 否 | 1=轉場, 0=不轉場 |
| 備註說明 | remark | textarea | 否 | 備註內容 |

### 3. JavaScript 功能

#### 自動計算服務時數
```javascript
function calculateServiceHours() {
    const time1 = $('#time1').val();
    const time2 = $('#time2').val();
    
    if (time1 && time2) {
        const start = new Date('2000-01-01 ' + time1);
        const end = new Date('2000-01-01 ' + time2);
        
        if (end > start) {
            const diffMs = end - start;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
            const totalMinutes = diffHours * 60 + diffMinutes;
            
            $('#service_hour').val(diffHours);
            $('#service_min').val(totalMinutes);
        }
    }
}
```

#### 表單驗證
- 服務日期：必填
- 使用者身分證號：必填
- 服務人員代碼：必填
- 服務時段：必填
- 服務類別：必填

#### 表單提交
```javascript
function saveRecord() {
    const formData = {
        company_id: $('#company_id').val(),
        id_number: $('#id_number').val(),
        srv_member: $('#srv_member').val(),
        service_date: $('#service_date').val(),
        time1: $('#time1').val(),
        time2: $('#time2').val(),
        service_type: $('#service_type').val(),
        srv_item: $('#srv_item').val(),
        service_status: $('input[name="service_status"]:checked').val(),
        traffic_times: $('input[name="traffic_times"]:checked').val(),
        remark: $('#remark').val()
        // ... 其他欄位
    };
    
    // 發送 AJAX 請求到 /everyDailyServiceRecords/save-record
}
```

### 4. 樣式特點

- **簡潔設計**: 使用表格布局，不使用複雜的卡片分類
- **原始風格**: 符合您提供的HTML結構和樣式
- **必填標示**: 紅色星號標示必填欄位
- **唯讀欄位**: 灰色背景表示唯讀欄位
- **響應式**: 支援行動裝置

### 5. 預設值

- 所屬單位：台北服務站
- 服務日期：今天
- 狀態：正常
- 是否轉場：轉場
- 服務時數：0

### 6. 路由更新

已更新 `routes/everyDailyServiceRecords.js`:
```javascript
// 新增記錄頁面
router.get('/create', renderVerifyMiddleware, function (req, res, next) {
    res.render('everyDailyServiceRecords/create', {
        title: '新增每日服務記錄',
        currentPage: 'everyDailyServiceRecords',
        // ... 其他參數
    });
});
```

### 7. 訪問路徑

- **新增頁面**: `/everyDailyServiceRecords/create`
- **保存API**: `/everyDailyServiceRecords/save-record`

### 8. 移除的複雜功能

相比原來的 record.ejs，新的 create.ejs 移除了：
- 複雜的卡片分類設計
- 不需要的欄位（如對象資訊、事由選項等）
- 複雜的 JavaScript 邏輯
- 過多的樣式定義

### 9. 保留的核心功能

- 基本的表單欄位
- 自動計算服務時數
- 表單驗證
- AJAX 提交
- 錯誤處理

## 完成狀態

✅ 簡化的新增頁面 HTML 結構
✅ 按照原始 HTML 設計的表格布局
✅ 移除不需要的欄位和分類
✅ JavaScript 基本功能
✅ 表單驗證邏輯
✅ 路由更新
✅ 響應式設計

新增頁面已完成，完全符合您的需求：簡潔、不分大類、按照原始HTML結構設計！
