# 日常服務記錄系統增強說明

## 概述
根據您的需求，我們已經成功增強了 dailyServiceRecords 系統，整合了 users 表和 physical_lt 表的數據，以提供更完整的服務記錄資訊。

## 修改內容

### 1. 後端修改 (`repository/dailyServiceRecords.js`)

#### 資料庫查詢增強
我們修改了 `getAllDailyServiceRecords` 函數，加入了以下關聯查詢：

```sql
SELECT 
    csr.*, -- 原有的 cs_service_records 所有欄位
    
    -- 從 users 表獲取姓名和地址
    u.full_name,
    u.address,
    
    -- 從 physical_lt 表獲取生理量測數據
    pl.temperature,
    pl.dbp,
    pl.sbp,
    pl.pulse,
    pl.breathe,
    pl.member_temperature
    
FROM cs_service_records csr
LEFT JOIN users u ON (csr.uid = u.uid AND u.role = 4)
LEFT JOIN physical_lt pl ON (
    csr.uid = pl.uid 
    AND DATE(pl.time) = csr.service_date 
    AND pl.is_deleted = 0
)
```

#### 關聯邏輯
1. **users 表關聯**: 
   - 條件: `csr.uid = u.uid AND u.role = 4`
   - 獲取: `full_name` (姓名), `address` (地址)

2. **physical_lt 表關聯**:
   - 條件: `csr.uid = pl.uid AND DATE(pl.time) = csr.service_date AND pl.is_deleted = 0`
   - 獲取: `temperature` (個案體溫), `dbp` (舒張壓), `sbp` (收縮壓), `pulse` (脈搏), `breathe` (呼吸), `member_temperature` (服務員體溫)

#### 表別名更新
- 將所有查詢條件更新為使用表別名 `csr` (cs_service_records)
- 確保 WHERE 條件正確引用表別名

### 2. 前端修改 (`views/dailyServiceRecords/index.ejs`)

#### 表格顯示增強
更新了 `displayRecords` 函數，現在顯示以下新增欄位：

1. **姓名**: `${record.full_name || ''}` - 從 users 表獲取
2. **服務員體溫**: `${record.member_temperature || '-'}` - 從 physical_lt 表獲取
3. **個案體溫**: `${record.temperature || '-'}` - 從 physical_lt 表獲取
4. **血壓(高)**: `${record.sbp || '-'}` - 從 physical_lt 表獲取
5. **血壓(低)**: `${record.dbp || '-'}` - 從 physical_lt 表獲取
6. **脈搏**: `${record.pulse || '-'}` - 從 physical_lt 表獲取
7. **呼吸**: `${record.breathe || '-'}` - 從 physical_lt 表獲取
8. **通訊區域**: `${record.address || '-'}` - 從 users 表獲取

#### 其他改進
- 修正了表格欄位的顯示邏輯
- 更新了 colspan 數量以匹配新的欄位數量
- 改進了日間/夜間時段的判斷邏輯

## 資料對應關係

### users 表
- **關聯條件**: `record.uid = users.uid AND users.role = 4`
- **獲取欄位**:
  - `full_name` → 顯示為「姓名」
  - `address` → 顯示為「通訊區域」

### physical_lt 表
- **關聯條件**: `record.uid = physical_lt.uid` (按日期匹配)
- **獲取欄位**:
  - `temperature` → 顯示為「個案體溫」
  - `member_temperature` → 顯示為「服務員體溫」
  - `sbp` → 顯示為「血壓(高)」
  - `dbp` → 顯示為「血壓(低)」
  - `pulse` → 顯示為「脈搏」
  - `breathe` → 顯示為「呼吸」

## 表格欄位完整列表

現在的表格包含以下 31 個欄位：

1. 編輯/刪除/複製
2. 狀態
3. 補卡
4. 案號 (puid)
5. **姓名** (full_name) ✨ 新增
6. 類別 (service_type)
7. 服務日期
8. 服務人員 (uid)
9. 服務時段
10. 服務時數(小時)
11. 服務時數(分鐘)
12. 轉場交通(分鐘)
13. 累計排班(小時)
14. 是否不轉場
15. 服務項目/備註
16. 服務順序
17. 服務金額
18. 歸屬年月
19. **服務員體溫** (member_temperature) ✨ 新增
20. **個案體溫** (temperature) ✨ 新增
21. **血壓(高)** (sbp) ✨ 新增
22. **血壓(低)** (dbp) ✨ 新增
23. **脈搏** (pulse) ✨ 新增
24. **呼吸** (breathe) ✨ 新增
25. **通訊區域** (address) ✨ 新增
26. 日間18點前
27. 夜間18點後
28. 建檔
29. 修改
30. 審核日

## 技術特點

### 1. 高效查詢
- 使用 LEFT JOIN 確保即使沒有對應的 users 或 physical_lt 記錄，服務記錄也會顯示
- 使用 DISTINCT 避免重複記錄

### 2. 資料完整性
- 當關聯表沒有對應資料時，顯示 "-" 而不是空白
- 保持原有功能的完整性

### 3. 效能優化
- 只在需要時進行關聯查詢
- 使用適當的索引條件

## 使用方式

### 訪問頁面
- 列表頁面: `http://localhost:3000/dailyServiceRecords`

### 查看效果
1. 進入日常服務記錄列表頁面
2. 執行搜尋查詢
3. 觀察表格中新增的欄位：
   - 姓名欄位顯示來自 users 表的 full_name
   - 生理量測欄位顯示來自 physical_lt 表的數據
   - 通訊區域顯示來自 users 表的 address

## 注意事項

### 1. 資料關聯
- users 表的關聯需要 `role = 4` 條件
- physical_lt 表的關聯會按日期匹配，可能一個服務記錄對應多個量測記錄

### 2. 效能考量
- 如果 physical_lt 表資料量很大，建議為 `(uid, time)` 添加複合索引
- 如果 users 表資料量很大，建議為 `(uid, role)` 添加複合索引

### 3. 資料顯示
- 當沒有對應資料時，顯示 "-"
- 生理量測數據可能會有多筆，目前取第一筆匹配的記錄

## 完成狀態
✅ 後端資料庫查詢增強
✅ users 表關聯 (姓名、地址)
✅ physical_lt 表關聯 (生理量測數據)
✅ 前端表格顯示更新
✅ 欄位對應關係建立
✅ 錯誤處理和資料驗證

系統已成功整合三個表格的數據，提供更完整的服務記錄資訊顯示。
