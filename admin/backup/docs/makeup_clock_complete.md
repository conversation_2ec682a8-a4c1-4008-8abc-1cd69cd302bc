# 補卡功能完整實現說明

## 概述
我已經完成了 everyDailyServiceRecords 補卡功能的完整實現，包括資料庫寫入邏輯和前端表單整合。

## 實現狀態

✅ **Repository 層補卡更新方法** - 已完成
✅ **路由層 API 端點實現** - 已完成  
✅ **前端表單資料收集修改** - 已完成
✅ **服務摘要欄位添加** - 已完成
✅ **自動計算實際服務分鐘數** - 已完成
✅ **完整的資料驗證** - 已完成
✅ **權限控制和安全性** - 已完成
✅ **錯誤處理機制** - 已完成
✅ **審計追蹤功能** - 已完成

## 主要實現內容

### 1. Repository 層 (`repository/everyDailyServiceRecords.js`)

新增了 `updateMakeupClock` 方法，更新以下資料庫欄位：
- `makeup_clock_reason` (補卡原因代碼)
- `makeup_clock_other_reason` (其他補卡原因說明)  
- `makeup_clock_by` (補卡操作人員)
- `makeup_clock_time` (補卡操作時間)
- `service_desc` (服務摘要)
- `service_desc_updated_by` (服務摘要更新人員)
- `service_desc_updated_at` (服務摘要更新時間)
- `clock_in_time` (上班打卡時間)
- `clock_out_time` (下班打卡時間)
- `actual_service_minutes` (實際服務分鐘數 - 自動計算)

### 2. 路由層 (`routes/everyDailyServiceRecords.js`)

完整實現了 `/save-makeup-clock` API 端點，包含：
- 必要欄位驗證 (記錄ID、補卡原因、打卡時間)
- 時間邏輯驗證 (下班時間必須晚於上班時間)
- 調用 repository 方法更新資料庫
- 完整的錯誤處理和回應

### 3. 前端頁面 (`views/everyDailyServiceRecords/makeup-clock.ejs`)

修改了表單資料收集，確保欄位名稱與後端一致：
```javascript
const formData = {
    id: $('#record_id').val(),
    clock_in_time: (inHour && inMinute) ? `${inHour}:${inMinute}` : null,
    clock_out_time: (outHour && outMinute) ? `${outHour}:${outMinute}` : null,
    makeup_clock_reason: $('input[name="makeup_reason"]:checked').val(),
    makeup_clock_other_reason: $('#makeup_other_reason').val(),
    service_desc: $('#service_desc').val() || null
};
```

新增了服務摘要欄位：
```html
<tr>
    <td class="label-cell">
        <span>服務摘要</span>
    </td>
    <td class="input-OneCell">
        <textarea id="service_desc" name="service_desc" class="TextWidth95percent" rows="3" placeholder="請輸入服務摘要..."></textarea>
    </td>
</tr>
```

## 功能特點

### 1. 自動計算實際服務分鐘數
系統會根據補卡的上班和下班時間自動計算實際服務分鐘數：
```javascript
let actualServiceMinutes = null;
if (data.clock_in_time && data.clock_out_time) {
    const clockIn = new Date(`1970-01-01 ${data.clock_in_time}`);
    const clockOut = new Date(`1970-01-01 ${data.clock_out_time}`);
    actualServiceMinutes = Math.round((clockOut - clockIn) / (1000 * 60));
}
```

### 2. 完整的資料驗證
- **記錄 ID 驗證**：確保有有效的記錄 ID
- **補卡原因驗證**：必須選擇補卡原因
- **時間驗證**：必須填寫上班和下班時間
- **時間邏輯驗證**：下班時間必須晚於上班時間

### 3. 權限控制
- 使用 `manage_oid` 確保只能更新自己組織的記錄
- 使用 `is_deleted = 0` 確保不會更新已刪除的記錄

### 4. 審計追蹤
- 記錄補卡操作人員 (`makeup_clock_by`)
- 記錄補卡操作時間 (`makeup_clock_time`)
- 記錄服務摘要更新人員和時間

## 使用流程

### 用戶操作流程
1. 在 index 頁面點擊「補卡」按鈕
2. 進入補卡頁面，查看服務記錄資訊
3. 選擇補卡原因
4. 填寫其他原因（如果選擇「其他」）
5. 設定上班和下班打卡時間
6. 填寫服務摘要（可選）
7. 點擊「儲存補卡」按鈕

### 系統處理流程
1. 前端收集表單資料
2. 發送 AJAX 請求到 `/save-makeup-clock`
3. 後端驗證資料完整性和邏輯
4. 調用 repository 更新資料庫
5. 自動計算實際服務分鐘數
6. 返回處理結果
7. 前端顯示成功或錯誤訊息

## 錯誤處理

### 常見錯誤情況
1. **缺少記錄 ID**：返回錯誤訊息
2. **未選擇補卡原因**：返回錯誤訊息
3. **未填寫打卡時間**：返回錯誤訊息
4. **時間邏輯錯誤**：下班時間早於上班時間
5. **資料庫錯誤**：連接失敗或查詢錯誤
6. **權限錯誤**：找不到記錄或無權限更新

## 測試建議

### 功能測試
1. 測試正常補卡流程
2. 測試各種錯誤情況的處理
3. 測試時間計算的正確性
4. 測試權限控制
5. 測試資料庫更新的正確性

### 測試資料範例
```javascript
const testData = {
    id: 123,
    makeup_clock_reason: '忘記打卡',
    makeup_clock_other_reason: '',
    clock_in_time: '09:00',
    clock_out_time: '17:00',
    service_desc: '完成居家照護服務'
};
```

## 總結

補卡功能已完全實現！系統現在可以：

1. **完整的補卡流程**：從前端表單到資料庫更新的完整流程
2. **自動計算服務時間**：根據打卡時間自動計算實際服務分鐘數
3. **完善的資料驗證**：確保資料完整性和邏輯正確性
4. **安全的權限控制**：確保只能更新有權限的記錄
5. **完整的審計追蹤**：記錄操作人員和時間
6. **友好的錯誤處理**：提供清晰的錯誤訊息

功能已準備就緒，可以立即使用！建議進行完整的功能測試以確保所有流程正常運作。
