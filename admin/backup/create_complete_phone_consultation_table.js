require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const dropTableSQL = `DROP TABLE IF EXISTS \`cs_phone_consultation_records\`;`;

const createTableSQL = `
CREATE TABLE \`cs_phone_consultation_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 1,
  \`project_id\` int(11) NOT NULL DEFAULT 1,
  \`created_by\` int(11) NOT NULL DEFAULT 1,
  
  -- 基本資訊
  \`call_date\` date DEFAULT NULL,
  \`contact_person_code\` varchar(50) DEFAULT NULL,
  \`contact_person_name\` varchar(100) DEFAULT NULL,
  
  -- 申請人資訊
  \`apply_name\` varchar(100) DEFAULT NULL,
  \`apply_gender\` varchar(10) DEFAULT NULL,
  \`apply_phone\` varchar(50) DEFAULT NULL,
  \`apply_relationship\` varchar(50) DEFAULT NULL,
  \`apply_email\` varchar(100) DEFAULT NULL,
  
  -- 轉介資訊
  \`duty_member_code\` varchar(50) DEFAULT NULL,
  \`duty_member_name\` varchar(100) DEFAULT NULL,
  \`data_source\` varchar(50) DEFAULT NULL,
  \`data_source_other\` varchar(100) DEFAULT NULL,
  \`referral_organization\` varchar(100) DEFAULT NULL,
  \`referral_department\` varchar(100) DEFAULT NULL,
  
  -- 案主資訊
  \`client_name\` varchar(100) DEFAULT NULL,
  \`client_id_number\` varchar(20) DEFAULT NULL,
  \`client_gender\` varchar(10) DEFAULT NULL,
  \`client_age\` int(11) DEFAULT NULL,
  \`client_height\` float DEFAULT NULL,
  \`client_weight\` float DEFAULT NULL,
  \`client_phone\` varchar(50) DEFAULT NULL,
  
  -- 地址資訊
  \`address_zipcode\` varchar(10) DEFAULT NULL,
  \`address_city\` varchar(50) DEFAULT NULL,
  \`address_district\` varchar(50) DEFAULT NULL,
  \`address_detail\` varchar(255) DEFAULT NULL,
  
  -- 健康狀況
  \`living_status\` varchar(50) DEFAULT NULL,
  \`languages\` text DEFAULT NULL,
  \`languages_other\` varchar(100) DEFAULT NULL,
  \`tube_placement\` text DEFAULT NULL,
  \`consciousness_status\` varchar(50) DEFAULT NULL,
  \`diseases_surgery\` text DEFAULT NULL,
  
  -- 行動能力
  \`mobility_ability\` varchar(50) DEFAULT NULL,
  \`transfer_needs\` varchar(50) DEFAULT NULL,
  \`mobility_restrictions\` text DEFAULT NULL,
  
  -- 服務需求
  \`service_gender_preference\` varchar(20) DEFAULT NULL,
  \`service_time_status\` varchar(50) DEFAULT NULL,
  \`daily_hours\` int(11) DEFAULT NULL,
  \`service_time_period\` text DEFAULT NULL,
  \`weekly_frequency\` int(11) DEFAULT NULL,
  \`service_days\` text DEFAULT NULL,
  
  -- 服務項目
  \`service_meal_prep\` text DEFAULT NULL,
  \`service_meal_prep_other\` varchar(255) DEFAULT NULL,
  \`service_laundry\` text DEFAULT NULL,
  \`service_laundry_other\` varchar(255) DEFAULT NULL,
  \`service_cleaning\` text DEFAULT NULL,
  \`service_cleaning_other\` varchar(255) DEFAULT NULL,
  \`service_assistance\` text DEFAULT NULL,
  \`service_assistance_other\` varchar(255) DEFAULT NULL,
  \`service_medical\` text DEFAULT NULL,
  \`service_medical_other\` varchar(255) DEFAULT NULL,
  \`service_companionship\` text DEFAULT NULL,
  \`service_companionship_other\` varchar(255) DEFAULT NULL,
  \`service_personal_care\` text DEFAULT NULL,
  \`service_personal_care_other\` varchar(255) DEFAULT NULL,
  \`service_special\` text DEFAULT NULL,
  \`service_special_other\` varchar(255) DEFAULT NULL,
  
  -- 客戶回應
  \`customer_response\` text DEFAULT NULL,
  \`customer_response_other\` varchar(255) DEFAULT NULL,
  \`customer_notes\` text DEFAULT NULL,
  
  -- 狀態追蹤
  \`current_status\` varchar(50) DEFAULT 'pending',
  \`priority_level\` varchar(20) DEFAULT 'normal',
  \`follow_up_required\` tinyint(1) DEFAULT 0,
  \`follow_up_date\` date DEFAULT NULL,
  
  -- 系統欄位
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0,
  \`deleted_by\` int(11) DEFAULT NULL,
  \`deleted_at\` timestamp NULL DEFAULT NULL,
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_by\` int(11) DEFAULT NULL,
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_call_date\` (\`call_date\`),
  KEY \`idx_contact_person_code\` (\`contact_person_code\`),
  KEY \`idx_client_id_number\` (\`client_id_number\`),
  KEY \`idx_current_status\` (\`current_status\`),
  KEY \`idx_is_delete\` (\`is_delete\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

const insertTestDataSQL = `
INSERT INTO \`cs_phone_consultation_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`call_date\`, \`contact_person_code\`, \`contact_person_name\`,
  \`apply_name\`, \`apply_gender\`, \`apply_phone\`, \`apply_relationship\`, \`apply_email\`,
  \`duty_member_code\`, \`duty_member_name\`, \`data_source\`,
  \`client_name\`, \`client_id_number\`, \`client_gender\`, \`client_age\`,
  \`address_city\`, \`address_district\`, \`address_detail\`,
  \`current_status\`, \`priority_level\`
) VALUES 
(1, 1, 1,
 '2025-07-07', 'B1234', '李庭怡',
 '王小明', '男', '0912345678', '本人', '<EMAIL>',
 'B1234', '李庭怡', '網路搜尋',
 '王小明', 'A123456789', '男', 65,
 '台北市', '大安區', '仁愛路四段123號',
 'pending', 'normal'),
(1, 1, 1,
 '2025-07-06', 'B1234', '李庭怡',
 '張美麗', '女', '0923456789', '女兒', '<EMAIL>',
 'B5678', '陳督導', '親友介紹',
 '張大華', 'B987654321', '男', 75,
 '台北市', '信義區', '松仁路100號',
 'completed', 'high');
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始重新創建電話諮詢記錄表格...');
    
    // 先刪除現有表格
    connection.query(dropTableSQL, (dropError, dropResults) => {
        if (dropError) {
            console.log('刪除表格時發生錯誤（可能表格不存在）:', dropError.message);
        } else {
            console.log('現有表格已刪除');
        }
        
        // 創建新表格
        connection.query(createTableSQL, (error, results) => {
            if (error) {
                console.error('創建表格錯誤:', error);
                connection.release();
                return;
            }
            
            console.log('表格創建成功！');
            
            // 插入測試資料
            connection.query(insertTestDataSQL, (insertError, insertResults) => {
                connection.release();
                
                if (insertError) {
                    console.error('插入測試資料錯誤:', insertError);
                    return;
                }
                
                console.log('測試資料插入成功！');
                console.log('電話諮詢記錄系統已準備就緒。');
            });
        });
    });
});
