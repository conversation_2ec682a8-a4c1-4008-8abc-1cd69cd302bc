<!DOCTYPE html>
<html>
<head>
    <title>服務狀態轉換測試</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>服務狀態代號轉換測試</h1>
    
    <div id="test-results"></div>

    <script>
        // 轉換服務狀態代號為中文文字
        function getServiceStatusText(statusCode) {
            const statusMap = {
                'N': '正常',
                'S': '自費', 
                'M': '未遇',
                'Z': '預定',
                'C': '取消',
                'R': '休息'
            };
            
            return statusMap[statusCode] || statusCode || '';
        }

        // 測試案例
        const testCases = [
            { code: 'N', expected: '正常' },
            { code: 'S', expected: '自費' },
            { code: 'M', expected: '未遇' },
            { code: 'Z', expected: '預定' },
            { code: 'C', expected: '取消' },
            { code: 'R', expected: '休息' },
            { code: '', expected: '' },
            { code: null, expected: '' },
            { code: undefined, expected: '' },
            { code: 'X', expected: 'X' } // 未知代號應該返回原值
        ];

        let results = '<table>';
        results += '<tr><th>狀態代號</th><th>預期結果</th><th>實際結果</th><th>測試結果</th></tr>';

        testCases.forEach(testCase => {
            const actual = getServiceStatusText(testCase.code);
            const passed = actual === testCase.expected;
            const status = passed ? '✅ 通過' : '❌ 失敗';
            const statusColor = passed ? 'green' : 'red';
            
            results += `<tr>
                <td>${testCase.code || '(空值)'}</td>
                <td>${testCase.expected || '(空值)'}</td>
                <td>${actual || '(空值)'}</td>
                <td style="color: ${statusColor};">${status}</td>
            </tr>`;
        });

        results += '</table>';

        // 顯示對應表
        results += '<h2>完整對應表</h2>';
        results += '<table>';
        results += '<tr><th>代號</th><th>中文名稱</th><th>說明</th></tr>';
        
        const statusMapping = [
            { code: 'N', text: '正常', desc: '正常服務' },
            { code: 'S', text: '自費', desc: '自費服務' },
            { code: 'M', text: '未遇', desc: '未遇到服務對象' },
            { code: 'Z', text: '預定', desc: '預定服務' },
            { code: 'C', text: '取消', desc: '取消服務' },
            { code: 'R', text: '休息', desc: '休息時間' }
        ];

        statusMapping.forEach(item => {
            results += `<tr>
                <td><strong>${item.code}</strong></td>
                <td>${item.text}</td>
                <td>${item.desc}</td>
            </tr>`;
        });

        results += '</table>';
        document.getElementById('test-results').innerHTML = results;
    </script>
</body>
</html>
