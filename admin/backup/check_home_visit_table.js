require('dotenv').config();
const mysqlConnection = require('../mysql/mysqlconnection');

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('檢查 cs_home_visit_records 表結構...');
    
    connection.query('DESCRIBE cs_home_visit_records', (error, results) => {
        if (error) {
            console.error('查詢表結構錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('現有表結構:');
        console.table(results);
        
        connection.release();
    });
});
