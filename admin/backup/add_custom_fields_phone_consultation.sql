-- 添加自定義欄位到電話諮詢記錄表格
-- 這些欄位用於儲存使用者自定義的時數、時段和頻率

USE vghks_db;

-- 添加三個自定義欄位
ALTER TABLE `cs_phone_consultation_records` 
ADD COLUMN `daily_hours_custom` varchar(50) DEFAULT NULL COMMENT '自定義每日時數' AFTER `daily_hours`,
ADD COLUMN `service_time_period_custom` varchar(100) DEFAULT NULL COMMENT '自定義服務時段' AFTER `service_time_period`,
ADD COLUMN `weekly_frequency_custom` varchar(50) DEFAULT NULL COMMENT '自定義每週頻率' AFTER `weekly_frequency`;

-- 檢查欄位是否成功添加
DESCRIBE `cs_phone_consultation_records`;
