require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_suspension_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊區域
  \`service_no\` varchar(50) DEFAULT NULL COMMENT '表單編號',
  \`suspension_start_date\` date DEFAULT NULL COMMENT '暫停開始日期',
  \`suspension_end_date\` date DEFAULT NULL COMMENT '暫停結束日期',
  
  -- 使用者資訊
  \`id_number\` varchar(20) DEFAULT NULL COMMENT '使用者身分證',
  \`user_name\` varchar(100) DEFAULT NULL COMMENT '使用者姓名',
  \`duty_member_code\` varchar(50) DEFAULT NULL COMMENT '社工/督導代碼',
  \`duty_member_name\` varchar(100) DEFAULT NULL COMMENT '社工/督導姓名',
  \`duty_member2_code\` varchar(50) DEFAULT NULL COMMENT '主責督導代碼',
  \`duty_member2_name\` varchar(100) DEFAULT NULL COMMENT '主責督導姓名',
  \`srv_member_code\` varchar(50) DEFAULT NULL COMMENT '服務人員代碼',
  \`srv_member_name\` varchar(100) DEFAULT NULL COMMENT '服務人員姓名',
  
  -- 暫停原因
  \`suspension_reasons\` text DEFAULT NULL COMMENT '暫停原因(JSON格式)',
  \`suspension_reason_other\` varchar(500) DEFAULT NULL COMMENT '其他暫停原因',
  
  -- 內容說明
  \`service_description\` text DEFAULT NULL COMMENT '內容說明',
  \`handling_result\` text DEFAULT NULL COMMENT '處理結果',
  
  -- 結案狀況
  \`close_status\` varchar(20) DEFAULT '期間內未結' COMMENT '結案狀況',
  
  -- 審核狀態
  \`review_status\` varchar(50) DEFAULT '未審核' COMMENT '審核狀態',
  \`review_date\` timestamp NULL DEFAULT NULL COMMENT '審核日期',
  \`review_by\` int(11) DEFAULT NULL COMMENT '審核者ID',
  \`review_comments\` text DEFAULT NULL COMMENT '審核意見',
  
  -- 狀態與時間戳記
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_suspension_start_date\` (\`suspension_start_date\`),
  KEY \`idx_suspension_end_date\` (\`suspension_end_date\`),
  KEY \`idx_id_number\` (\`id_number\`),
  KEY \`idx_review_status\` (\`review_status\`),
  KEY \`idx_close_status\` (\`close_status\`),
  KEY \`idx_is_delete\` (\`is_delete\`),
  KEY \`idx_created_at\` (\`created_at\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='暫停記錄表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_suspension_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`service_no\`, \`suspension_start_date\`, \`suspension_end_date\`,
  \`id_number\`, \`user_name\`, \`duty_member_code\`, \`duty_member_name\`,
  \`duty_member2_code\`, \`duty_member2_name\`, \`srv_member_code\`, \`srv_member_name\`,
  \`suspension_reasons\`, \`suspension_reason_other\`, \`service_description\`, \`handling_result\`,
  \`close_status\`, \`review_status\`
) VALUES (
  1, 1, 1,
  'SR20250703001', '2025-07-01', '2025-07-15',
  'A123456789', '王小明', 'B1234', '李庭怡',
  'B5678', '陳督導', 'C5678', '陳照服員',
  '["住院", "其他"]', '因跌倒住院治療', 
  '案主因跌倒住院，需暫停居家照顧服務。已與家屬聯繫，確認住院期間照護安排。',
  '已協助家屬聯繫醫院社工，安排出院後的照護計畫。預計7月15日出院後恢復服務。',
  '期間內未結', '已審核'
);
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建暫停記錄表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('暫停記錄系統已準備就緒。');
        });
    });
});
