require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_daily_service_records\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 基本資訊區域
  \`service_no\` varchar(50) DEFAULT NULL COMMENT '表單編號',
  \`service_date\` date DEFAULT NULL COMMENT '服務日期',
  \`service_time_start\` time DEFAULT NULL COMMENT '開始時間',
  \`service_time_end\` time DEFAULT NULL COMMENT '結束時間',
  \`service_method\` varchar(20) DEFAULT NULL COMMENT '進行方式',
  
  -- 使用者資訊
  \`id_number\` varchar(20) DEFAULT NULL COMMENT '使用者身分證',
  \`user_name\` varchar(100) DEFAULT NULL COMMENT '使用者姓名',
  \`duty_member_code\` varchar(50) DEFAULT NULL COMMENT '社工/督導代碼',
  \`duty_member_name\` varchar(100) DEFAULT NULL COMMENT '社工/督導姓名',
  \`service_type\` varchar(20) DEFAULT NULL COMMENT '服務類別',
  
  -- 對象資訊
  \`targets\` text DEFAULT NULL COMMENT '對象(JSON格式)',
  \`srv_member_code\` varchar(50) DEFAULT NULL COMMENT '服務人員代碼',
  \`srv_member_name\` varchar(100) DEFAULT NULL COMMENT '服務人員姓名',
  \`family\` varchar(100) DEFAULT NULL COMMENT '家屬',
  \`other_people\` varchar(100) DEFAULT NULL COMMENT '其他人員',
  
  -- 事由內容
  \`service_items\` text DEFAULT NULL COMMENT '事由選項(JSON格式)',
  \`service_item_other\` varchar(500) DEFAULT NULL COMMENT '其他事由',
  \`other_requirements\` varchar(500) DEFAULT NULL COMMENT '其他需求',
  \`service_description\` text DEFAULT NULL COMMENT '內容說明',
  
  -- 完成日期
  \`expected_completion_date\` date DEFAULT NULL COMMENT '應完成日',
  \`actual_completion_date\` date DEFAULT NULL COMMENT '實完成日',
  
  -- 審核狀態
  \`review_status\` varchar(50) DEFAULT '未審核' COMMENT '審核狀態',
  \`review_date\` timestamp NULL DEFAULT NULL COMMENT '審核日期',
  \`review_by\` int(11) DEFAULT NULL COMMENT '審核者ID',
  \`review_comments\` text DEFAULT NULL COMMENT '審核意見',
  
  -- 拋轉設定
  \`upload_setting\` varchar(20) DEFAULT '不拋轉' COMMENT '拋轉設定',
  
  -- 狀態與時間戳記
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_service_date\` (\`service_date\`),
  KEY \`idx_id_number\` (\`id_number\`),
  KEY \`idx_review_status\` (\`review_status\`),
  KEY \`idx_is_delete\` (\`is_delete\`),
  KEY \`idx_created_at\` (\`created_at\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日常服務記錄表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_daily_service_records\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`service_no\`, \`service_date\`, \`service_time_start\`, \`service_time_end\`, \`service_method\`,
  \`id_number\`, \`user_name\`, \`duty_member_code\`, \`duty_member_name\`, \`service_type\`,
  \`targets\`, \`srv_member_code\`, \`srv_member_name\`, \`family\`, \`other_people\`,
  \`service_items\`, \`service_item_other\`, \`other_requirements\`, \`service_description\`,
  \`expected_completion_date\`, \`actual_completion_date\`,
  \`review_status\`, \`upload_setting\`
) VALUES (
  1, 1, 1,
  'DSR20250703001', '2025-07-03', '09:00:00', '10:30:00', '電話',
  'A123456789', '王小明', 'B1234', '李庭怡', '照服',
  '["本人", "家屬"]', 'C5678', '陳照服員', '王太太', '',
  '["與案家討論服務內容調整", "提供諮詢服務"]', '', '需要增加復健服務時數',
  '與案家討論目前的照顧服務狀況，家屬反映希望能增加復健相關的服務項目，已協助聯繫相關資源。',
  '2025-07-10', '2025-07-08',
  '已審核', '不拋轉'
);
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建日常服務記錄表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('日常服務記錄系統已準備就緒。');
        });
    });
});
