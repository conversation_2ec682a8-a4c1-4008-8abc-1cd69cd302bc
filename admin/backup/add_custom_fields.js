require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const addCustomFieldsSQL = `
ALTER TABLE \`cs_phone_consultation_records\` 
ADD COLUMN \`daily_hours_custom\` varchar(50) DEFAULT NULL COMMENT '自定義每日時數' AFTER \`daily_hours\`,
ADD COLUMN \`service_time_period_custom\` varchar(100) DEFAULT NULL COMMENT '自定義服務時段' AFTER \`service_time_period\`,
ADD COLUMN \`weekly_frequency_custom\` varchar(50) DEFAULT NULL COMMENT '自定義每週頻率' AFTER \`weekly_frequency\`;
`;

const checkFieldsSQL = `
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'cs_phone_consultation_records' 
AND COLUMN_NAME IN ('daily_hours_custom', 'service_time_period_custom', 'weekly_frequency_custom')
ORDER BY ORDINAL_POSITION;
`;

async function addCustomFields() {
    try {
        console.log('開始添加自定義欄位...');

        // 執行 ALTER TABLE
        await new Promise((resolve, reject) => {
            mysqlConnection.getConnection((connectionError, connection) => {
                if (connectionError) {
                    reject(connectionError);
                    return;
                }

                connection.query(addCustomFieldsSQL, (error, results) => {
                    connection.release();
                    if (error) {
                        if (error.code === 'ER_DUP_FIELDNAME') {
                            console.log('欄位已存在，跳過添加');
                            resolve(results);
                        } else {
                            reject(error);
                        }
                    } else {
                        console.log('自定義欄位添加成功');
                        resolve(results);
                    }
                });
            });
        });

        // 檢查欄位是否存在
        const checkResults = await new Promise((resolve, reject) => {
            mysqlConnection.getConnection((connectionError, connection) => {
                if (connectionError) {
                    reject(connectionError);
                    return;
                }

                connection.query(checkFieldsSQL, (error, results) => {
                    connection.release();
                    if (error) {
                        reject(error);
                    } else {
                        resolve(results);
                    }
                });
            });
        });

        console.log('檢查結果：');
        console.table(checkResults);

        console.log('自定義欄位添加完成！');
        process.exit(0);
    } catch (error) {
        console.error('添加自定義欄位時發生錯誤：', error);
        process.exit(1);
    }
}

addCustomFields();
