<!DOCTYPE html>
<html>
<head>
    <title>時間計算測試</title>
</head>
<body>
    <h1>日間/夜間服務時數計算測試</h1>
    
    <div id="test-results"></div>

    <script>
        // 將時間字串 (HH:MM:SS 或 HH:MM) 轉換為分鐘數
        function timeToMinutes(timeString) {
            if (!timeString) return 0;
            
            const parts = timeString.split(':');
            const hours = parseInt(parts[0]) || 0;
            const minutes = parseInt(parts[1]) || 0;
            
            return hours * 60 + minutes;
        }

        // 計算日間18點前的服務時數
        function calculateDaytimeHours(time1, time2) {
            if (!time1 || !time2) return '0.00';
            
            // 將時間字串轉換為分鐘數
            const startMinutes = timeToMinutes(time1);
            const endMinutes = timeToMinutes(time2);
            const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘
            
            let daytimeMinutes = 0;
            
            if (endMinutes <= cutoffMinutes) {
                // 完全在18點前
                daytimeMinutes = endMinutes - startMinutes;
            } else if (startMinutes < cutoffMinutes) {
                // 跨越18點
                daytimeMinutes = cutoffMinutes - startMinutes;
            }
            // 如果完全在18點後，daytimeMinutes 保持為 0
            
            return (daytimeMinutes / 60).toFixed(2);
        }

        // 計算夜間18點後的服務時數
        function calculateNighttimeHours(time1, time2) {
            if (!time1 || !time2) return '0.00';
            
            // 將時間字串轉換為分鐘數
            const startMinutes = timeToMinutes(time1);
            const endMinutes = timeToMinutes(time2);
            const cutoffMinutes = 18 * 60; // 18:00 = 1080分鐘
            
            let nighttimeMinutes = 0;
            
            if (startMinutes >= cutoffMinutes) {
                // 完全在18點後
                nighttimeMinutes = endMinutes - startMinutes;
            } else if (endMinutes > cutoffMinutes) {
                // 跨越18點
                nighttimeMinutes = endMinutes - cutoffMinutes;
            }
            // 如果完全在18點前，nighttimeMinutes 保持為 0
            
            return (nighttimeMinutes / 60).toFixed(2);
        }

        // 測試案例
        const testCases = [
            { time1: '08:00', time2: '12:00', desc: '完全在日間 (8:00-12:00)' },
            { time1: '19:00', time2: '22:00', desc: '完全在夜間 (19:00-22:00)' },
            { time1: '16:00', time2: '20:00', desc: '跨越18點 (16:00-20:00)' },
            { time1: '17:30', time2: '18:30', desc: '跨越18點 (17:30-18:30)' },
            { time1: '18:00', time2: '21:00', desc: '剛好18點開始 (18:00-21:00)' },
            { time1: '15:00', time2: '18:00', desc: '剛好18點結束 (15:00-18:00)' },
            { time1: '14:30', time2: '19:45', desc: '跨越18點長時間 (14:30-19:45)' }
        ];

        let results = '<table border="1" style="border-collapse: collapse; width: 100%;">';
        results += '<tr><th>時間範圍</th><th>描述</th><th>日間時數</th><th>夜間時數</th><th>總時數</th></tr>';

        testCases.forEach(testCase => {
            const daytime = calculateDaytimeHours(testCase.time1, testCase.time2);
            const nighttime = calculateNighttimeHours(testCase.time1, testCase.time2);
            const total = (parseFloat(daytime) + parseFloat(nighttime)).toFixed(2);
            
            results += `<tr>
                <td>${testCase.time1} - ${testCase.time2}</td>
                <td>${testCase.desc}</td>
                <td>${daytime}</td>
                <td>${nighttime}</td>
                <td>${total}</td>
            </tr>`;
        });

        results += '</table>';
        document.getElementById('test-results').innerHTML = results;
    </script>
</body>
</html>
