require('dotenv').config();
const mysqlConnection = require('./mysql/mysqlconnection');

const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`cs_service_questionnaires\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT,
  \`manage_oid\` int(11) NOT NULL DEFAULT 0 COMMENT '管理組織ID',
  \`project_id\` int(11) NOT NULL DEFAULT 0 COMMENT '專案ID',
  \`created_by\` int(11) NOT NULL DEFAULT 0 COMMENT '建立者ID',
  
  -- 表單資訊
  \`service_no\` varchar(50) DEFAULT NULL COMMENT '表單編號',
  \`service_date\` date DEFAULT NULL COMMENT '服務日期',
  \`service_time_start\` time DEFAULT NULL COMMENT '服務開始時間',
  \`service_time_end\` time DEFAULT NULL COMMENT '服務結束時間',
  \`conduct_method\` varchar(100) DEFAULT NULL COMMENT '進行方式',
  
  -- 人員資訊
  \`id_number\` varchar(20) DEFAULT NULL COMMENT '使用者身分證',
  \`user_name\` varchar(100) DEFAULT NULL COMMENT '使用者姓名',
  \`duty_member_code\` varchar(50) DEFAULT NULL COMMENT '填表人代碼',
  \`duty_member_name\` varchar(100) DEFAULT NULL COMMENT '填表人姓名',
  \`duty_member2_code\` varchar(50) DEFAULT NULL COMMENT '主責督導代碼',
  \`duty_member2_name\` varchar(100) DEFAULT NULL COMMENT '主責督導姓名',
  \`srv_member_code\` varchar(50) DEFAULT NULL COMMENT '服務人員代碼',
  \`srv_member_name\` varchar(100) DEFAULT NULL COMMENT '服務人員姓名',
  \`family_other\` varchar(200) DEFAULT NULL COMMENT '家屬/其他',
  
  -- 對象和事由
  \`targets\` text DEFAULT NULL COMMENT '對象(JSON格式)',
  \`service_item\` varchar(200) DEFAULT NULL COMMENT '事由選項',
  \`service_item_other\` varchar(500) DEFAULT NULL COMMENT '其他事由說明',
  
  -- 內容
  \`service_description\` text DEFAULT NULL COMMENT '內容說明',
  
  -- 審核狀態
  \`sign_status\` tinyint(1) DEFAULT 0 COMMENT '審核狀態(0=未審核, 1=已審核)',
  \`sign_date\` timestamp NULL DEFAULT NULL COMMENT '審核日期',
  \`sign_by\` int(11) DEFAULT NULL COMMENT '審核者ID',
  
  -- 狀態與時間戳記
  \`is_delete\` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否刪除',
  \`deleted_by\` int(11) DEFAULT NULL COMMENT '刪除者ID',
  \`deleted_at\` timestamp NULL DEFAULT NULL COMMENT '刪除時間',
  \`created_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
  \`updated_at\` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
  
  PRIMARY KEY (\`id\`),
  KEY \`idx_manage_oid\` (\`manage_oid\`),
  KEY \`idx_service_date\` (\`service_date\`),
  KEY \`idx_id_number\` (\`id_number\`),
  KEY \`idx_duty_member_code\` (\`duty_member_code\`),
  KEY \`idx_duty_member2_code\` (\`duty_member2_code\`),
  KEY \`idx_srv_member_code\` (\`srv_member_code\`),
  KEY \`idx_sign_status\` (\`sign_status\`),
  KEY \`idx_is_delete\` (\`is_delete\`),
  KEY \`idx_created_at\` (\`created_at\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服務問卷表';
`;

const insertTestDataSQL = `
INSERT INTO \`cs_service_questionnaires\` (
  \`manage_oid\`, \`project_id\`, \`created_by\`,
  \`service_no\`, \`service_date\`, \`service_time_start\`, \`service_time_end\`, \`conduct_method\`,
  \`id_number\`, \`user_name\`, \`duty_member_code\`, \`duty_member_name\`,
  \`duty_member2_code\`, \`duty_member2_name\`, \`srv_member_code\`, \`srv_member_name\`,
  \`family_other\`, \`targets\`, \`service_item\`, \`service_item_other\`,
  \`service_description\`, \`sign_status\`
) VALUES 
(
  1, 1, 1,
  'SQ20250707001', '2025-07-07', '09:00:00', '10:00:00', '面對面訪談',
  'A123456789', '王小明', 'B1234', '李庭怡',
  'B5678', '陳督導', 'C5678', '陳照服員',
  '王太太(女兒)', '["本人", "家屬"]', '定期服務滿意度調查',
  '針對居家照顧服務品質進行調查',
  '<p>本次問卷調查主要了解案主對於居家照顧服務的滿意度，包括：</p><ul><li>服務人員的專業度</li><li>服務時間的準確性</li><li>服務內容的完整性</li></ul><p>案主表示整體滿意，建議持續保持服務品質。</p>',
  1
),
(
  1, 1, 1,
  'SQ20250706001', '2025-07-06', '14:00:00', '15:00:00', '電話訪談',
  'B987654321', '李阿嬤', 'B1234', '李庭怡',
  'B5678', '陳督導', 'C9999', '林照服員',
  '李先生(兒子)', '["本人", "家屬"]', '服務異常後續追蹤',
  '上週服務異常事件後續滿意度調查',
  '<p>針對上週發生的服務異常事件進行後續追蹤：</p><p><strong>事件概述：</strong>照服員遲到30分鐘</p><p><strong>改善措施：</strong>已重新安排服務時程並加強時間管理訓練</p><p><strong>案主回饋：</strong>對改善措施表示滿意，願意繼續接受服務</p>',
  0
);
`;

mysqlConnection.getConnection((err, connection) => {
    if (err) {
        console.error('資料庫連接錯誤:', err);
        return;
    }

    console.log('開始創建服務問卷表格...');
    
    connection.query(createTableSQL, (error, results) => {
        if (error) {
            console.error('創建表格錯誤:', error);
            connection.release();
            return;
        }
        
        console.log('表格創建成功！');
        
        // 插入測試資料
        connection.query(insertTestDataSQL, (insertError, insertResults) => {
            connection.release();
            
            if (insertError) {
                console.error('插入測試資料錯誤:', insertError);
                return;
            }
            
            console.log('測試資料插入成功！');
            console.log('服務問卷系統已準備就緒。');
        });
    });
});
