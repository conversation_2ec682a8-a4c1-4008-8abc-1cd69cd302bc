const express = require('express');
const router = express.Router();
const EveryDailyServiceRepository = require('../repository/everyDailyServiceRecords');

// 中間件
const renderVerifyMiddleware = (req, res, next) => {
    if (!req.cookies.vghks_account) {
        return res.redirect('/login');
    }
    next();
};

const apiVerifyMiddleware = (req, res, next) => {
    if (!req.cookies.vghks_account) {
        return res.status(401).send({
            'state': "error",
            'msg': "未授權的訪問"
        });
    }
    next();
};

// 列表頁面
router.get('/', renderVerifyMiddleware, function (req, res, next) {
    let cookie_acc = req.cookies.vghks_account;
    let cookie_name = req.cookies.vghks_name;
    let cookie_uid = req.cookies.vghks_uid;
    let slider = req.cookies.vghks_slider;
    let role = parseInt(req.cookies.vghks_role) || 0;
    let role_name = req.cookies.vghks_role_name;
    let o_name = req.cookies.vghks_o_name;
    let manage_oid = req.cookies.vghks_manage_oid;

    res.render('everyDailyServiceRecords/index', {
        title: '日常服務記錄',
        currentPage: 'everyDailyServiceRecords',
        cookie_acc: cookie_acc,
        cookie_name: cookie_name,
        name: cookie_name,
        cookie_uid: cookie_uid,
        slider: slider,
        role: role,
        role_name: role_name,
        o_name: o_name,
        manage_oid: manage_oid
    });
});

// 新增記錄頁面
router.get('/create', renderVerifyMiddleware, function (req, res, next) {
    let cookie_acc = req.cookies.vghks_account;
    let cookie_name = req.cookies.vghks_name;
    let cookie_uid = req.cookies.vghks_uid;
    let slider = req.cookies.vghks_slider;
    let role = parseInt(req.cookies.vghks_role) || 0;
    let role_name = req.cookies.vghks_role_name;
    let o_name = req.cookies.vghks_o_name;
    let manage_oid = req.cookies.vghks_manage_oid;

    res.render('everyDailyServiceRecords/create', {
        title: '新增日常服務記錄',
        currentPage: 'everyDailyServiceRecords',
        mode: 'create',
        record: null,
        cookie_acc: cookie_acc,
        cookie_name: cookie_name,
        name: cookie_name,
        cookie_uid: cookie_uid,
        slider: slider,
        role: role,
        role_name: role_name,
        o_name: o_name,
        manage_oid: manage_oid
    });
});

// 補卡頁面
router.get('/makeup-clock/:id', renderVerifyMiddleware, function (req, res, next) {
    let cookie_acc = req.cookies.vghks_account;
    let cookie_name = req.cookies.vghks_name;
    let cookie_uid = req.cookies.vghks_uid;
    let slider = req.cookies.vghks_slider;
    let role = parseInt(req.cookies.vghks_role) || 0;
    let role_name = req.cookies.vghks_role_name;
    let o_name = req.cookies.vghks_o_name;
    let manage_oid = req.cookies.vghks_manage_oid;

    res.render('everyDailyServiceRecords/makeup-clock', {
        title: '補卡作業',
        currentPage: 'everyDailyServiceRecords',
        recordId: req.params.id,
        cookie_acc: cookie_acc,
        cookie_name: cookie_name,
        name: cookie_name,
        cookie_uid: cookie_uid,
        slider: slider,
        role: role,
        role_name: role_name,
        o_name: o_name,
        manage_oid: manage_oid
    });
});
// 編輯記錄頁面
router.get('/edit/:id', renderVerifyMiddleware, async function (req, res, next) {
    try {
        const recordId = req.params.id;
        const manage_oid = req.cookies.vghks_manage_oid;

        const record = await EveryDailyServiceRepository.getEveryDailyServiceRecordById(recordId, manage_oid);

        if (!record) {
            return res.status(404).send('記錄不存在');
        }

        let cookie_acc = req.cookies.vghks_account;
        let cookie_name = req.cookies.vghks_name;
        let cookie_uid = req.cookies.vghks_uid;
        let slider = req.cookies.vghks_slider;
        let role = parseInt(req.cookies.vghks_role) || 0;
        let role_name = req.cookies.vghks_role_name;
        let o_name = req.cookies.vghks_o_name;

        res.render('everyDailyServiceRecords/edit', {
            title: '編輯服務記錄',
            currentPage: 'everyDailyServiceRecords',
            recordId: req.params.id,
            cookie_acc: cookie_acc,
            cookie_name: cookie_name,
            name: cookie_name,
            cookie_uid: cookie_uid,
            slider: slider,
            role: role,
            role_name: role_name,
            o_name: o_name,
            manage_oid: manage_oid
        });
    } catch (error) {
        console.error('編輯頁面錯誤:', error);
        res.status(500).send('伺服器錯誤');
    }
});

// API: 獲取單筆記錄數據 (用於編輯頁面)
router.get('/data/:id', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const recordId = req.params.id;
        const manage_oid = req.cookies.vghks_manage_oid;

        const record = await EveryDailyServiceRepository.getEveryDailyServiceRecordById(recordId, manage_oid);

        if (!record) {
            return res.send({
                state: 'error',
                msg: '記錄不存在'
            });
        }

        res.send({
            state: 'success',
            data: record
        });
    } catch (error) {
        console.error('獲取記錄數據錯誤:', error);
        res.send({
            state: 'error',
            msg: '獲取記錄失敗: ' + error.message
        });
    }
});

// API: 儲存補卡資料
router.post('/save-makeup-clock', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const data = req.body;
        const manage_oid = req.cookies.vghks_manage_oid;
        const updated_by = req.cookies.vghks_uid;

        const recordId = data.id;

        // 驗證必要欄位
        if (!recordId) {
            return res.send({
                state: 'error',
                msg: '缺少記錄 ID'
            });
        }

        if (!data.makeup_clock_reason) {
            return res.send({
                state: 'error',
                msg: '請選擇補卡原因'
            });
        }

        if (!data.clock_in_time || !data.clock_out_time) {
            return res.send({
                state: 'error',
                msg: '請填寫上班和下班打卡時間'
            });
        }

        // 驗證時間格式和邏輯
        const clockInTime = data.clock_in_time;
        const clockOutTime = data.clock_out_time;

        if (clockInTime >= clockOutTime) {
            return res.send({
                state: 'error',
                msg: '下班時間必須晚於上班時間'
            });
        }

        // 調用 repository 更新補卡資料
        const result = await EveryDailyServiceRepository.updateMakeupClock(
            recordId,
            data,
            manage_oid,
            updated_by
        );

        res.send({
            state: 'success',
            msg: result.message,
            data: result
        });
    } catch (error) {
        console.error('儲存補卡資料錯誤:', error);
        res.send({
            state: 'error',
            msg: error.message || '儲存補卡資料失敗'
        });
    }
});

// API: 更新記錄
router.post('/update', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const data = req.body;
        const manage_oid = req.cookies.vghks_manage_oid;
        const updated_by = req.cookies.vghks_uid;

        if (data.id) {
            // 更新記錄
            await EveryDailyServiceRepository.updateEveryDailyServiceRecord(data.id, data, manage_oid, updated_by);
            res.send({
                state: 'success',
                msg: '記錄更新成功'
            });
        } else {
            // 新增記錄
            const result = await EveryDailyServiceRepository.createEveryDailyServiceRecord(data);
            res.send({
                state: 'success',
                msg: '記錄新增成功',
                id: result.id
            });
        }
    } catch (error) {
        console.error('更新記錄錯誤:', error);
        res.send({
            state: 'error',
            msg: '更新失敗: ' + error.message
        });
    }
});

// API: 查詢日常服務記錄
router.post('/records', apiVerifyMiddleware, async function (req, res, next) {
    let manage_oid = req.cookies.vghks_manage_oid;
    const searchParams = req.body;

    try {
        const result = await EveryDailyServiceRepository.getAllEveryDailyServiceRecords(searchParams, manage_oid);

        res.send({
            state: 'success',
            data: result.records,
            total: result.total,
            page: result.page,
            pageSize: result.pageSize,
            totalPages: result.totalPages
        });
    } catch (error) {
        console.error('查詢記錄錯誤:', error);
        res.send({
            state: 'error',
            msg: '查詢失敗: ' + error.message
        });
    }
});

// API: 保存記錄
router.post('/save-record', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const data = req.body;
        const manage_oid = req.cookies.vghks_manage_oid;

        if (data.id) {
            // 更新記錄
            await EveryDailyServiceRepository.updateEveryDailyServiceRecord(data.id, data, manage_oid);
            res.send({
                state: 'success',
                msg: '記錄更新成功'
            });
        } else {
            // 新增記錄
            const result = await EveryDailyServiceRepository.createEveryDailyServiceRecord(data);
            res.send({
                state: 'success',
                msg: '記錄新增成功',
                id: result.id
            });
        }
    } catch (error) {
        console.error('保存記錄錯誤:', error);
        res.send({
            state: 'error',
            msg: '保存失敗: ' + error.message
        });
    }
});

// API: 刪除記錄
router.post('/delete-record/:id', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const recordId = req.params.id;
        const manage_oid = req.cookies.vghks_manage_oid;
        const deleted_by = req.cookies.vghks_uid;

        await EveryDailyServiceRepository.deleteEveryDailyServiceRecord(recordId, manage_oid, deleted_by);

        res.send({
            state: 'success',
            msg: '記錄刪除成功'
        });
    } catch (error) {
        console.error('刪除記錄錯誤:', error);
        res.send({
            state: 'error',
            msg: '刪除失敗: ' + error.message
        });
    }
});

// API: 更新審核狀態
router.post('/update-review/:id', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const recordId = req.params.id;
        const { status, comments } = req.body;
        const reviewBy = req.cookies.vghks_uid;

        await EveryDailyServiceRepository.updateReviewStatus(recordId, status, reviewBy, comments);

        res.send({
            state: 'success',
            msg: '審核狀態更新成功'
        });
    } catch (error) {
        console.error('更新審核狀態錯誤:', error);
        res.send({
            state: 'error',
            msg: '更新失敗: ' + error.message
        });
    }
});

// 更新服務摘要
router.post('/update-service-desc', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const { recordId, serviceDesc } = req.body;
        const updatedBy = req.cookies.vghks_account;

        if (!recordId) {
            return res.status(400).json({
                state: "error",
                msg: "缺少記錄ID"
            });
        }

        const result = await EveryDailyServiceRepository.updateServiceDescription(recordId, serviceDesc, updatedBy);

        if (result.success) {
            res.json({
                state: "success",
                msg: "服務摘要更新成功",
                data: result.data
            });
        } else {
            res.status(500).json({
                state: "error",
                msg: result.message || "更新失敗"
            });
        }
    } catch (error) {
        console.error('更新服務摘要錯誤:', error);
        res.status(500).json({
            state: "error",
            msg: "系統錯誤"
        });
    }
});

// API: 取得系統代碼
router.get('/api/system-codes/:codeGroup', apiVerifyMiddleware, async function (req, res, next) {
    try {
        const codeGroup = req.params.codeGroup;
        const codes = await EveryDailyServiceRepository.getSystemCodes(codeGroup);

        res.send({
            state: 'success',
            data: codes
        });
    } catch (error) {
        console.error('取得系統代碼錯誤:', error);
        res.send({
            state: 'error',
            msg: '取得系統代碼失敗: ' + error.message
        });
    }
});

module.exports = router;
