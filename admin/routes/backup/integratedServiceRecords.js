const express = require('express');
const router = express.Router();
const IntegratedServiceRepository = require('../repository/integratedServiceRecords');

// 中間件：檢查用戶是否已登入
const renderVerifyMiddleware = (req, res, next) => {
    let cookie_acc = req.cookies.vghks_account;
    if (cookie_acc == null) {
        res.redirect('/logout');
        return;
    }
    next();
};

// 中間件：API 驗證
const apiVerifyMiddleware = (req, res, next) => {
    let cookie_acc = req.cookies.vghks_account;
    if (!cookie_acc) {
        res.send({
            state: 'error',
            msg: 'not verified'
        });
        return;
    }
    next();
};

// 整合服務記錄列表頁面
router.get('/', renderVerifyMiddleware, function (req, res) {
    let cookie_acc = req.cookies.vghks_account;
    let cookie_name = req.cookies.vghks_name;
    let cookie_uid = req.cookies.vghks_uid;
    let slider = req.cookies.vghks_slider;
    let role = req.cookies.vghks_role;
    let role_name = req.cookies.vghks_role_name;
    let o_name = req.cookies.vghks_o_name;
    let manage_oid = req.cookies.vghks_manage_oid;

    res.render('integratedServiceRecords/index', {
        title: '整合服務記錄查詢',
        currentPage: 'integratedServiceRecords',
        cookie_acc: cookie_acc,
        cookie_name: cookie_name,
        name: cookie_name,
        cookie_uid: cookie_uid,
        slider: slider,
        role: role,
        role_name: role_name,
        o_name: o_name,
        manage_oid: manage_oid
    });
});

// 查看整合服務記錄詳情
router.get('/view/:id', renderVerifyMiddleware, async function (req, res) {
    let cookie_acc = req.cookies.vghks_account;
    let cookie_name = req.cookies.vghks_name;
    let cookie_uid = req.cookies.vghks_uid;
    let slider = req.cookies.vghks_slider;
    let role = req.cookies.vghks_role;
    let role_name = req.cookies.vghks_role_name;
    let o_name = req.cookies.vghks_o_name;
    let manage_oid = req.cookies.vghks_manage_oid;

    try {
        const recordId = req.params.id;
        const record = await IntegratedServiceRepository.getIntegratedServiceRecordById(recordId);

        if (!record) {
            return res.redirect('/integratedServiceRecords');
        }

        res.render('integratedServiceRecords/view', {
            title: '查看整合服務記錄',
            currentPage: 'integratedServiceRecords',
            record: record,
            cookie_acc: cookie_acc,
            cookie_name: cookie_name,
            name: cookie_name,
            cookie_uid: cookie_uid,
            slider: slider,
            role: role,
            role_name: role_name,
            o_name: o_name,
            manage_oid: manage_oid
        });
    } catch (error) {
        console.error('獲取整合服務記錄詳情錯誤:', error);
        res.redirect('/integratedServiceRecords');
    }
});

// API: 獲取整合服務記錄列表
router.post('/search', apiVerifyMiddleware, async function (req, res) {
    try {
        const searchParams = req.body;
        const page = parseInt(req.body.page) || 1;
        const pageSize = parseInt(req.body.pageSize) || 10;

        const result = await IntegratedServiceRepository.getIntegratedServiceRecords(searchParams, page, pageSize);

        // 格式化日期和時間
        const formattedRecords = result.records.map(record => {
            // 格式化日期
            if (record.service_date) {
                const date = new Date(record.service_date);
                record.service_date_formatted = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            }

            // 格式化時間
            if (record.service_start_time) {
                record.service_start_time_formatted = record.service_start_time.toString().substring(0, 5);
            }
            if (record.service_end_time) {
                record.service_end_time_formatted = record.service_end_time.toString().substring(0, 5);
            }

            // 格式化服務時段
            if (record.service_start_time && record.service_end_time) {
                record.service_time_range = `${record.service_start_time_formatted} - ${record.service_end_time_formatted}`;
            }

            // 格式化建檔/修改時間
            if (record.created_at) {
                const createdDate = new Date(record.created_at);
                record.created_at_formatted = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
            }
            if (record.updated_at) {
                const updatedDate = new Date(record.updated_at);
                record.updated_at_formatted = `${updatedDate.getFullYear()}-${String(updatedDate.getMonth() + 1).padStart(2, '0')}-${String(updatedDate.getDate()).padStart(2, '0')} ${String(updatedDate.getHours()).padStart(2, '0')}:${String(updatedDate.getMinutes()).padStart(2, '0')}`;
            }
            if (record.review_date) {
                const reviewDate = new Date(record.review_date);
                record.review_date_formatted = `${reviewDate.getFullYear()}-${String(reviewDate.getMonth() + 1).padStart(2, '0')}-${String(reviewDate.getDate()).padStart(2, '0')}`;
            } else {
                record.review_date_formatted = '';
            }

            return record;
        });

        res.send({
            state: 'success',
            records: formattedRecords,
            total: result.total,
            page: result.page,
            pageSize: result.pageSize,
            totalPages: result.totalPages
        });
    } catch (error) {
        console.error('搜尋整合服務記錄錯誤:', error);
        res.send({
            state: 'error',
            msg: '搜尋失敗: ' + error.message
        });
    }
});

// API: 獲取整合服務記錄詳情
router.get('/data/:id', apiVerifyMiddleware, async function (req, res) {
    try {
        const recordId = req.params.id;
        const record = await IntegratedServiceRepository.getIntegratedServiceRecordById(recordId);

        if (!record) {
            return res.send({
                state: 'error',
                msg: '找不到記錄'
            });
        }

        res.send({
            state: 'success',
            record: record
        });
    } catch (error) {
        console.error('獲取整合服務記錄詳情錯誤:', error);
        res.send({
            state: 'error',
            msg: '獲取失敗: ' + error.message
        });
    }
});

module.exports = router;
