const mysqlConnection = require('../mysql/mysqlconnection');

// 取得所有日常服務記錄
exports.getAllEveryDailyServiceRecords = (searchParams, manage_oid) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }
            
            let whereConditions = ['csr.is_deleted = 0', 'csr.oid = ?'];
            let queryParams = [searchParams.organization_id];//manage_oid

            // 搜尋條件
            if (searchParams.id_number) {
                whereConditions.push('csr.puid LIKE ?');
                queryParams.push(`%${searchParams.id_number}%`);
            }

            if (searchParams.service_date_start && searchParams.service_date_end) {
                whereConditions.push('csr.service_date BETWEEN ? AND ?');
                queryParams.push(searchParams.service_date_start, searchParams.service_date_end);
            } else if (searchParams.service_date_start) {
                whereConditions.push('csr.service_date >= ?');
                queryParams.push(searchParams.service_date_start);
            } else if (searchParams.service_date_end) {
                whereConditions.push('csr.service_date <= ?');
                queryParams.push(searchParams.service_date_end);
            }

            if (searchParams.service_year_month) {
                whereConditions.push('csr.service_ym = ?');
                queryParams.push(searchParams.service_year_month.replace('-', ''));
            }

            if (searchParams.duty_member_code) {
                whereConditions.push('cue.employee_number = ?');
                queryParams.push(`%${searchParams.duty_member_code}%`);
            }

            if (searchParams.service_type && searchParams.service_type !== 'ALL') {
                whereConditions.push('csr.service_type = ?');
                queryParams.push(searchParams.service_type);
            }

            if (searchParams.srv_member_code) {
                whereConditions.push('cue.employee_number = ?');
                queryParams.push(`${searchParams.srv_member_code}`);
            }

            // if (searchParams.service_item && searchParams.service_item !== 'ALL') {
            //     whereConditions.push('csr.service_items LIKE ?');
            //     queryParams.push(`%${searchParams.service_item}%`);
            // }

            if (searchParams.service_status && searchParams.service_status !== 'ALL') {
                whereConditions.push('csr.service_status = ?');
                queryParams.push(searchParams.service_status);
            }

            const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

            // 計算總數 - 更新為使用別名
            const countQuery = `
                   SELECT COUNT(DISTINCT csr.id) as total
                     FROM cs_service_records csr
                LEFT JOIN users u ON (csr.uid = u.uid AND u.role = 4)
                LEFT JOIN cs_employee_information cue ON (csr.uid = cue.uid)
                LEFT JOIN physical_lt pl ON (csr.id = pl.records_id AND pl.is_deleted = 0)
                ${whereClause}
            `;
            connection.query(countQuery, queryParams, (countError, countResults) => {
                if (countError) {
                    connection.release();
                    reject(countError);
                    return;
                }

                const total = countResults[0].total;
                const page = parseInt(searchParams.page) || 1;
                const pageSize = parseInt(searchParams.pageSize) || 50;
                const offset = (page - 1) * pageSize;
                const totalPages = Math.ceil(total / pageSize);

                // 查詢資料 - 加入 users 表和 physical_lt 表的關聯
                const dataQuery = `
                    SELECT
                        csr.id, csr.company_id, csr.service_ym, csr.service_date, csr.service_type, csr.time1, csr.time2,
                        csr.service_hour, csr.service_min, csr.traffic_min, csr.traffic_times, csr.service_status,
                        csr.salary, csr.salary_t, csr.salary1, csr.salary2, csr.salary3, csr.salary3_t,
                        csr.salary_hour, csr.salary_hour_t, csr.salary_hour1, csr.salary_hour2, csr.salary_hour3, csr.salary_hour3_t,
                        csr.srv_item_a, csr.times, csr.srv_item, csr.srv_item_checked,
                        csr.remark, csr.sign_date, csr.sign_out_date, csr.sign_user,
                        csr.schedule_id, csr.puid, csr.is_from_schedule, csr.original_srv_member,
                        csr.change_reason, csr.change_type,
                        csr.approval_status, csr.approval_user, csr.approval_date,
                        csr.clock_in_time, csr.clock_out_time, csr.has_clock_in, csr.has_clock_out,
                        csr.actual_service_minutes,
                        csr.uid, csr.original_uid, csr.oid,
                        csr.service_desc, csr.service_desc_updated_by, csr.service_desc_updated_at,
                        csr.created_at, csr.updated_at, csr.is_deleted,

                        -- 從 users 表獲取姓名和地址
                        u.full_name,
                        u.address,

                        -- 從 users 表獲取服務人員代碼和姓名
                        u2.full_name AS duty_member_name,

                        -- 從 cs_user_information 表獲取案號
                        cui.record_number,
                        -- 從 cu_user_employee_information 表獲取員工編號
                        cue.employee_number,

                        -- 從 physical_lt 表獲取生理量測數據
                        pl.temperature,
                        pl.dbp,
                        pl.sbp,
                        pl.pulse,
                        pl.breathe,
                        pl.member_temperature,

                        -- 計算服務總價
                        (
                            SELECT COALESCE(SUM(s.service_price), 0)
                            FROM service s
                            WHERE FIND_IN_SET(s.service_code, csr.srv_item) > 0
                        ) AS service_total_price

                         FROM cs_service_records csr
                    LEFT JOIN users u ON (csr.puid = u.uid AND u.role = 4)
                    LEFT JOIN cs_user_information cui ON (csr.puid = cui.uid)
                    LEFT JOIN users u2 ON (csr.uid = u2.uid)
                    LEFT JOIN cs_employee_information cue ON (csr.uid = cue.uid)
                    LEFT JOIN physical_lt pl ON (csr.id = pl.records_id AND pl.is_deleted = 0)
                    ${whereClause}
                    ORDER BY csr.service_date DESC, csr.created_at DESC
                    LIMIT ? OFFSET ?
                `;

                const finalParams = [...queryParams, pageSize, offset];
                connection.query(dataQuery, finalParams, (error, results) => {
                    connection.release();

                    if (error) {
                        reject(error);
                        return;
                    }

                    // 為每個記錄計算服務價格
                    const processRecordsWithPrice = async () => {
                        const recordsWithPrice = [];

                        for (const record of results) {
                            try {
                                const serviceTotalPrice = await exports.calculateServiceTotalPrice(record.srv_item);
                                recordsWithPrice.push({
                                    ...record,
                                    service_total_price: serviceTotalPrice
                                });
                            } catch (error) {
                                console.error('計算服務價格錯誤:', error);
                                recordsWithPrice.push({
                                    ...record,
                                    service_total_price: 0
                                });
                            }
                        }

                        return recordsWithPrice;
                    };

                    processRecordsWithPrice()
                        .then(recordsWithPrice => {
                            resolve({
                                records: recordsWithPrice,
                                total: total,
                                page: page,
                                pageSize: pageSize,
                                totalPages: totalPages
                            });
                        })
                        .catch(error => {
                            console.error('處理記錄錯誤:', error);
                            resolve({
                                records: results.map(record => ({...record, service_total_price: 0})),
                                total: total,
                                page: page,
                                pageSize: pageSize,
                                totalPages: totalPages
                            });
                        });
                });
            });
        });
    });
};

// 根據ID查詢單一日常服務記錄
exports.getEveryDailyServiceRecordById = (id, manage_oid) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }
            const query = `
            SELECT
                csr.id, csr.company_id, csr.service_ym, csr.service_date, csr.service_type, csr.time1, csr.time2,
                csr.service_hour, csr.service_min, csr.traffic_min, csr.traffic_times, csr.service_status,
                csr.salary, csr.salary_t, csr.salary1, csr.salary2, csr.salary3, csr.salary3_t,
                csr.salary_hour, csr.salary_hour_t, csr.salary_hour1, csr.salary_hour2, csr.salary_hour3, csr.salary_hour3_t,
                csr.srv_item_a, csr.times, csr.srv_item, csr.srv_item_checked,
                csr.remark, csr.sign_date, csr.sign_out_date, csr.sign_user,
                csr.schedule_id, csr.puid, csr.is_from_schedule, csr.original_srv_member,
                csr.change_reason, csr.change_type,
                csr.approval_status, csr.approval_user, csr.approval_date,
                csr.clock_in_time, csr.clock_out_time, csr.has_clock_in, csr.has_clock_out,
                csr.actual_service_minutes,
                csr.uid, csr.original_uid, csr.oid,
                csr.created_at, csr.updated_at, csr.is_deleted,

                -- 從 users 表獲取姓名和地址
                u.full_name,
                u.address,

                -- 從 users 表獲取服務人員代碼和姓名
                u2.full_name AS duty_member_name,

                -- 從 cs_user_information 表獲取案號
                cui.record_number,
                -- 從 cs_employee_information 表獲取員工編號
                cue.employee_number,

                -- 從 physical_lt 表獲取生理量測數據
                pl.temperature,
                pl.dbp,
                pl.sbp,
                pl.pulse,
                pl.breathe,
                pl.member_temperature,

                -- 計算服務總價
                (
                    SELECT COALESCE(SUM(s.service_price), 0)
                    FROM service s
                    WHERE FIND_IN_SET(s.service_code, csr.srv_item) > 0
                ) AS service_total_price

            FROM cs_service_records csr
            LEFT JOIN users u ON (csr.puid = u.uid AND u.role = 4)
            LEFT JOIN cs_user_information cui ON (csr.puid = cui.uid)
            LEFT JOIN users u2 ON (csr.uid = u2.uid)
            LEFT JOIN cs_employee_information cue ON (csr.uid = cue.uid)
            LEFT JOIN physical_lt pl ON (csr.id = pl.records_id AND pl.is_deleted = 0)
            WHERE csr.id = ? AND csr.oid = ? AND csr.is_deleted = 0
            `;

            connection.query(query, [id, manage_oid], (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                if (results.length === 0) {
                    resolve(null);
                    return;
                }

                resolve(results[0]);
            });
        });
    });
};

// 建立新的日常服務記錄
exports.createEveryDailyServiceRecord = (data) => {
    
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection(async (err, connection) => {
            if (err) {
                connection.release();
                reject(err);
                return;
            }
            
            // 先查出 puid
            connection.query(
                'SELECT uid FROM cs_user_information WHERE record_number = ?',
                 [data.user_id], (error, results) => {
                    if (error) {
                        connection.release();
                        reject(error);
                        return;
                    }
                    if (results.length === 0) {
                        reject(new Error('找不到對應的使用者 (puid)'));
                        return;
                    }
                    const puid = results[0].uid;


                    // 先查出 uid
                    connection.query(
                        'SELECT uid FROM cs_employee_information WHERE employee_number = ?',
                        [data.srv_member_code], (error, results) => {

                            if (error) {
                                connection.release();
                                reject(error);
                                return;
                            }
                            if (results.length === 0) {
                                reject(new Error('找不到對應的員工 (uid)'));
                                return;
                            }
                            const uid = results[0].uid;
                            const service_ym = `${new Date(data.service_date).getFullYear()}${String(new Date(data.service_date).getMonth() + 1).padStart(2, '0')}`;
            
                            //console.log('Creating service record with data:', data);
                            
                            const query = `
                                INSERT INTO cs_service_records (
                                    oid, puid, uid, service_ym, service_date, service_type, time1, time2,
                                    service_hour, service_min, traffic_min, traffic_times, service_status,
                                    srv_item, remark
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            `;

                            const values = [
                                data.organization_select, puid, uid, service_ym, data.service_date, data.service_type, data.time1, data.time2,
                                    data.service_hour, data.service_min, data.traffic_min, data.traffic_times, data.service_status,
                                    data.srv_item, data.remark
                            ];
                            
                            // 執行插入操作
                            connection.query(query, values, (error, results) => {
                                connection.release();

                                if (error) {
                                    reject(error);
                                    return;
                                }

                                resolve({
                                    id: results.insertId,
                                    message: '日常服務記錄建立成功'
                                });
                            });
                        });
                });
        });
    });
};

// 更新日常服務記錄
exports.updateEveryDailyServiceRecord = (id, data, manage_oid, updated_by) => {  
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            // 先查出 uid
            connection.query(
                'SELECT uid FROM cs_employee_information WHERE employee_number = ?',
                [data.srv_member_code], (error, results) => {

                    if (error) {
                        connection.release();
                        reject(error);
                        return;
                    }
                    if (results.length === 0) {
                        reject(new Error('找不到對應的員工 (uid)'));
                        return;
                    }
                    const uid = results[0].uid;
                    const service_ym = `${new Date(data.service_date).getFullYear()}${String(new Date(data.service_date).getMonth() + 1).padStart(2, '0')}`;
                    
                    const query = `
                        UPDATE cs_service_records SET
                            service_status = ?, service_type = ?, service_date = ?, service_ym = ?, uid = ?, time1 = ?,
                            time2 = ?, traffic_min = ?, traffic_times = ?, srv_item = ?, remark = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ? AND oid = ? AND is_deleted = 0
                    `;

                    const values = [
                        data.service_status, data.service_type, data.service_date, service_ym, uid, data.time1,
                        data.time2, data.traffic_min, data.traffic_times, data.srv_item, data.remark,
                        id, manage_oid
                    ];

                    connection.query(query, values, (error, results) => {

                        if (error) {
                            reject(error);
                            return;
                        }

                        if (results.affectedRows === 0) {
                            reject(new Error('記錄不存在或無權限修改'));
                            return;
                        }

                        const countQuery = `
                        SELECT COUNT(DISTINCT pl.vid) as total
                            FROM physical_lt pl WHERE pl.records_id = ? and pl.is_deleted = 0
                        `;
                        const queryParams = [
                            data.id
                        ];
                        connection.query(countQuery, queryParams, (countError, countResults) => {
                            if (!countError && countResults.length > 0) {
                                const updatePhysicalQuery= `
                                    UPDATE physical_lt SET
                                    temperature = ?, member_temperature = ?, sbp = ?, dbp = ?, pulse = ?, breathe = ?, modify_id = ?, modify_time = CURRENT_TIMESTAMP
                                    WHERE records_id = ? AND is_deleted = 0
                                `;
                                const physicalValues = [
                                    data.temperature, data.member_temperature, data.sbp, data.dbp, data.pulse, data.breathe, updated_by,
                                    data.id
                                ];
                                connection.query(updatePhysicalQuery, physicalValues, (error, results) => {
                                    resolve({
                                        message: '每日服務記錄更新成功'
                                    });
                                });
                            }
                        });

                        resolve({
                            message: '每日服務記錄更新成功'
                        });
                    });
            });

        
        });
    });
};

// 軟刪除日常服務記錄
exports.deleteEveryDailyServiceRecord = (id, manage_oid, deleted_by) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            const query = `
                UPDATE cs_service_records
                SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND oid = ? AND is_deleted = 0
            `;

            connection.query(query, [id, manage_oid], (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                if (results.affectedRows === 0) {
                    reject(new Error('記錄不存在或已被刪除'));
                    return;
                }

                resolve({
                    message: '日常服務記錄刪除成功'
                });
            });
        });
    });
};

// 更新審核狀態
exports.updateReviewStatus = (id, status, reviewBy, comments) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            const query = `
                UPDATE cs_service_records
                SET service_status = ?, review_by = ?, review_comments = ?, review_date = CURRENT_TIMESTAMP
                WHERE id = ? AND is_deleted = 0
            `;

            connection.query(query, [status, reviewBy, comments, id], (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                resolve({
                    message: '審核狀態更新成功'
                });
            });
        });
    });
};

// 更新補卡資料
exports.updateMakeupClock = (id, data, manage_oid, updated_by) => {
    console.log('更新補卡資料:', id, data, manage_oid, updated_by);
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }
            // const sdate_query = `
            // SELECT csr.service_date
            // FROM cs_service_records csr
            // WHERE csr.id = ? AND csr.oid = ? AND csr.is_deleted = 0
            // `;

            // connection.query(sdate_query, [id, manage_oid], (error, results) => {
            //     if (error) {
            //         reject(error);
            //         return;
            //     }

            //     if (results.length === 0) {
            //         resolve(null);
            //         return;
            //     }

            //     results[0].service_date
            // });



            // 計算實際服務分鐘數
            let actualServiceMinutes = null;
            if (data.clock_in_time && data.clock_out_time) {
                const clockIn = new Date(`1970-01-01 ${data.clock_in_time}`);
                const clockOut = new Date(`1970-01-01 ${data.clock_out_time}`);
                actualServiceMinutes = Math.round((clockOut - clockIn) / (1000 * 60));
            }

            const query = `
                UPDATE cs_service_records SET
                    makeup_clock_reason = ?,
                    makeup_clock_other_reason = ?,
                    makeup_clock_by = ?,
                    makeup_clock_time = CURRENT_TIMESTAMP,
                    service_desc = ?,
                    service_desc_updated_by = ?,
                    service_desc_updated_at = CURRENT_TIMESTAMP,
                    clock_in_time = CASE
                        WHEN ? IS NOT NULL THEN CONCAT(service_date, ' ', ?)
                        ELSE clock_in_time
                    END,
                    clock_out_time = CASE
                        WHEN ? IS NOT NULL THEN CONCAT(service_date, ' ', ?)
                        ELSE clock_out_time
                    END,
                    has_clock_in = 1,
                    has_clock_out = 1,
                    actual_service_minutes = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND oid = ? AND is_deleted = 0
            `;

            const values = [
                data.makeup_clock_reason,
                data.makeup_clock_other_reason,
                updated_by,
                data.service_desc,
                updated_by,
                data.clock_in_time,  // 第一個 CASE 的檢查參數
                data.clock_in_time,  // 第一個 CASE 的時間值
                data.clock_out_time, // 第二個 CASE 的檢查參數
                data.clock_out_time, // 第二個 CASE 的時間值
                actualServiceMinutes,
                id,
                manage_oid
            ];

            connection.query(query, values, (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                if (results.affectedRows === 0) {
                    reject(new Error('找不到要更新的記錄或無權限更新'));
                    return;
                }

                resolve({
                    success: true,
                    message: '補卡資料更新成功',
                    affectedRows: results.affectedRows
                });
            });
        });
    });
};

// 計算服務項目總價
exports.calculateServiceTotalPrice = (srvItem) => {
    return new Promise((resolve, reject) => {
        if (!srvItem) {
            resolve(0);
            return;
        }

        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            // 將服務項目字串分割成陣列
            const serviceCodes = srvItem.split(',').map(code => code.trim()).filter(code => code);

            if (serviceCodes.length === 0) {
                connection.release();
                resolve(0);
                return;
            }

            // 建立 IN 查詢的參數
            const placeholders = serviceCodes.map(() => '?').join(',');
            const query = `
                SELECT COALESCE(SUM(service_price), 0) AS total_price
                FROM service
                WHERE service_code IN (${placeholders})
            `;

            connection.query(query, serviceCodes, (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                const totalPrice = results[0]?.total_price || 0;
                resolve(totalPrice);
            });
        });
    });
};

// 更新服務摘要
exports.updateServiceDescription = (recordId, serviceDesc, updatedBy) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            const updateQuery = `
                UPDATE cs_service_records
                SET service_desc = ?,
                    service_desc_updated_by = ?,
                    service_desc_updated_at = NOW()
                WHERE id = ?
            `;

            connection.query(updateQuery, [serviceDesc, updatedBy, recordId], (error, results) => {
                connection.release();

                if (error) {
                    reject(error);
                    return;
                }

                if (results.affectedRows === 0) {
                    resolve({
                        success: false,
                        message: "找不到指定的記錄"
                    });
                } else {
                    // 取得更新後的時間戳記
                    const selectQuery = `
                        SELECT service_desc_updated_at
                        FROM cs_service_records
                        WHERE id = ?
                    `;

                    mysqlConnection.getConnection((err, connection2) => {
                        if (err) {
                            resolve({
                                success: true,
                                message: "更新成功",
                                data: { updatedAt: new Date().toISOString() }
                            });
                            return;
                        }

                        connection2.query(selectQuery, [recordId], (error2, results2) => {
                            connection2.release();

                            const updatedAt = results2[0]?.service_desc_updated_at || new Date();
                            resolve({
                                success: true,
                                message: "更新成功",
                                data: { updatedAt: updatedAt }
                            });
                        });
                    });
                }
            });
        });
    });
};
