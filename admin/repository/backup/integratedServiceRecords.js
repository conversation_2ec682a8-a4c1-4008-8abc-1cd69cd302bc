const mysqlConnection = require('../mysql/mysqlconnection');

/**
 * 整合查詢日常服務記錄和生理量測數據
 * @param {Object} searchParams - 搜尋參數
 * @param {string} searchParams.company_id - 所屬單位
 * @param {string} searchParams.user_id - 使用者ID
 * @param {string} searchParams.service_year_month - 服務年月
 * @param {string} searchParams.service_date_start - 服務開始日期
 * @param {string} searchParams.service_date_end - 服務結束日期
 * @param {string} searchParams.srv_member_code - 服務人員代碼
 * @param {string} searchParams.service_type - 服務類別
 * @param {string} searchParams.service_status - 服務狀態
 * @param {number} page - 頁碼
 * @param {number} pageSize - 每頁筆數
 * @returns {Promise<Object>} - 查詢結果和總筆數
 */
exports.getIntegratedServiceRecords = (searchParams, page = 1, pageSize = 10) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            // 基本查詢條件
            let whereConditions = [];
            let queryParams = [];

            // 搜尋條件
            if (searchParams.company_id && searchParams.company_id !== '(全部)') {
                whereConditions.push('sr.company_id = ?');
                queryParams.push(searchParams.company_id);
            }

            if (searchParams.user_id) {
                whereConditions.push('sr.id_number LIKE ?');
                queryParams.push(`%${searchParams.user_id}%`);
            }

            if (searchParams.service_year_month) {
                whereConditions.push('sr.service_ym = ?');
                queryParams.push(searchParams.service_year_month);
            }

            if (searchParams.service_date_start && searchParams.service_date_end) {
                whereConditions.push('sr.service_date BETWEEN ? AND ?');
                queryParams.push(searchParams.service_date_start, searchParams.service_date_end);
            } else if (searchParams.service_date_start) {
                whereConditions.push('sr.service_date >= ?');
                queryParams.push(searchParams.service_date_start);
            } else if (searchParams.service_date_end) {
                whereConditions.push('sr.service_date <= ?');
                queryParams.push(searchParams.service_date_end);
            }

            if (searchParams.srv_member_code) {
                whereConditions.push('sr.srv_member LIKE ?');
                queryParams.push(`%${searchParams.srv_member_code}%`);
            }

            if (searchParams.service_type && searchParams.service_type !== '(全部)') {
                whereConditions.push('sr.service_type = ?');
                queryParams.push(searchParams.service_type);
            }

            if (searchParams.service_status && searchParams.service_status !== '(全部)') {
                whereConditions.push('sr.service_status = ?');
                queryParams.push(searchParams.service_status);
            }

            // 構建 WHERE 子句
            const whereClause = whereConditions.length > 0 
                ? 'WHERE ' + whereConditions.join(' AND ') 
                : '';

            // 計算總筆數的查詢
            const countQuery = `
                SELECT COUNT(*) AS total
                FROM cs_service_records sr
                LEFT JOIN physical_lt pl ON (
                    pl.records_id = sr.id
                    OR (pl.uid = sr.id_number AND DATE(pl.time) = sr.service_date)
                    OR (pl.member_code = sr.srv_member AND DATE(pl.time) = sr.service_date)
                )
                ${whereClause}
                AND (pl.is_deleted = 0 OR pl.is_deleted IS NULL)
            `;

            // 查詢資料的 SQL
            const dataQuery = `
                SELECT 
                    -- 服務基本資訊
                    sr.id AS service_record_id,
                    sr.company_id,
                    sr.service_ym AS service_year_month,
                    sr.srv_member,
                    sr.id_number,
                    sr.service_date,
                    sr.service_type,
                    sr.time1 AS service_start_time,
                    sr.time2 AS service_end_time,
                    sr.service_hour,
                    sr.service_min,
                    sr.traffic_min,
                    sr.traffic_times,
                    CASE 
                        WHEN sr.service_status = 'N' THEN '正常'
                        WHEN sr.service_status = 'C' THEN '取消'
                        WHEN sr.service_status = 'Z' THEN '暫停'
                        WHEN sr.service_status = 'R' THEN '休息'
                        WHEN sr.service_status = 'S' THEN '純自費'
                        WHEN sr.service_status = 'M' THEN '見面'
                        ELSE sr.service_status
                    END AS service_status,
                    sr.srv_item AS service_items,
                    sr.remark AS remarks,
                    sr.sign_date AS review_date,
                    sr.sign_user AS review_by,
                    
                    -- 生理量測資訊
                    pl.temperature AS user_temperature,
                    pl.member_temperature AS staff_temperature,
                    pl.pulse,
                    pl.breathe,
                    pl.sbp AS blood_pressure_high,
                    pl.dbp AS blood_pressure_low,
                    pl.spo2 AS blood_oxygen,
                    pl.sugar AS blood_sugar,
                    
                    -- 其他資訊
                    CASE 
                        WHEN TIME(sr.time1) < '18:00:00' THEN '是'
                        ELSE '否'
                    END AS before_18,
                    CASE 
                        WHEN TIME(sr.time2) >= '18:00:00' THEN '是'
                        ELSE '否'
                    END AS after_18,
                    
                    -- 建檔/修改資訊
                    sr.created_at,
                    sr.updated_at,
                    pl.modify_time AS physical_updated_at
                FROM 
                    cs_service_records sr
                LEFT JOIN 
                    physical_lt pl ON (
                        pl.records_id = sr.id
                        OR (pl.uid = sr.id_number AND DATE(pl.time) = sr.service_date)
                        OR (pl.member_code = sr.srv_member AND DATE(pl.time) = sr.service_date)
                    )
                ${whereClause}
                AND (pl.is_deleted = 0 OR pl.is_deleted IS NULL)
                ORDER BY 
                    sr.service_date DESC, sr.time1 ASC
                LIMIT ?, ?
            `;

            // 執行計算總筆數的查詢
            connection.query(countQuery, queryParams, (countErr, countResults) => {
                if (countErr) {
                    connection.release();
                    reject(countErr);
                    return;
                }

                const total = countResults[0].total;

                // 計算分頁參數
                const offset = (page - 1) * pageSize;
                const paginationParams = [...queryParams, offset, parseInt(pageSize)];

                // 執行查詢資料的 SQL
                connection.query(dataQuery, paginationParams, (dataErr, dataResults) => {
                    connection.release();
                    
                    if (dataErr) {
                        reject(dataErr);
                        return;
                    }

                    resolve({
                        records: dataResults,
                        total: total,
                        page: page,
                        pageSize: pageSize,
                        totalPages: Math.ceil(total / pageSize)
                    });
                });
            });
        });
    });
};

/**
 * 根據ID獲取整合的服務記錄詳情
 * @param {number} id - 服務記錄ID
 * @returns {Promise<Object>} - 服務記錄詳情
 */
exports.getIntegratedServiceRecordById = (id) => {
    return new Promise((resolve, reject) => {
        mysqlConnection.getConnection((err, connection) => {
            if (err) {
                reject(err);
                return;
            }

            const query = `
                SELECT 
                    -- 服務基本資訊
                    sr.id AS service_record_id,
                    sr.company_id,
                    sr.service_ym AS service_year_month,
                    sr.srv_member,
                    sr.id_number,
                    sr.service_date,
                    sr.service_type,
                    sr.time1 AS service_start_time,
                    sr.time2 AS service_end_time,
                    sr.service_hour,
                    sr.service_min,
                    sr.traffic_min,
                    sr.traffic_times,
                    sr.service_status,
                    sr.srv_item AS service_items,
                    sr.remark AS remarks,
                    sr.sign_date AS review_date,
                    sr.sign_user AS review_by,
                    
                    -- 生理量測資訊
                    pl.temperature AS user_temperature,
                    pl.member_temperature AS staff_temperature,
                    pl.pulse,
                    pl.breathe,
                    pl.sbp AS blood_pressure_high,
                    pl.dbp AS blood_pressure_low,
                    pl.spo2 AS blood_oxygen,
                    pl.sugar AS blood_sugar,
                    
                    -- 建檔/修改資訊
                    sr.created_at,
                    sr.updated_at,
                    pl.modify_time AS physical_updated_at
                FROM 
                    cs_service_records sr
                LEFT JOIN 
                    physical_lt pl ON (
                        pl.records_id = sr.id
                        OR (pl.uid = sr.id_number AND DATE(pl.time) = sr.service_date)
                        OR (pl.member_code = sr.srv_member AND DATE(pl.time) = sr.service_date)
                    )
                WHERE 
                    sr.id = ?
                    AND (pl.is_deleted = 0 OR pl.is_deleted IS NULL)
                LIMIT 1
            `;

            connection.query(query, [id], (error, results) => {
                connection.release();
                
                if (error) {
                    reject(error);
                    return;
                }

                resolve(results.length > 0 ? results[0] : null);
            });
        });
    });
};
